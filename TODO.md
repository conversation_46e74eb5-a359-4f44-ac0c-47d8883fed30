# TODO: RAG System Enhancements

## 🚀 High Priority Improvements

### 1. LLM-Generated Chunk Summaries
**Status**: 📋 Planned  
**Priority**: High  
**Estimated Impact**: +15-25% success rate improvement

#### Description
Process each code chunk with an LLM to generate 1-3 sentence summaries describing the chunk's purpose, functionality, and role in the larger system.

#### Implementation Options
- **Option 1 (Recommended)**: Dual Embedding - Store both original code AND summary
- **Option 2**: Enhanced Content - Prepend summary to original code content
- **Option 3**: Summary-Only - Replace code with summaries (not recommended)

#### Expected Benefits
- **Semantic Matching**: Better understanding of "how does X work?" queries
- **Pattern Recognition**: Improved detection of architectural patterns
- **Cross-Reference**: Better relationships between related code chunks
- **Similarity Scores**: Expected improvement from ~0.254 to 0.4-0.6
- **Context Utilization**: Expected improvement from ~60% to 75-85%

#### Implementation Details
```python
# Example prompt for LLM processing
prompt = """
Analyze this code chunk and provide a 1-3 sentence summary describing:
1. What this code does (primary purpose)
2. Key functionality or algorithms used  
3. How it fits into the larger system

Code chunk:
{chunk_content}

Summary:
"""
```

#### Cost Estimation
- **Processing Time**: ~19 hours for 2,283 chunks (30-60s per chunk)
- **LLM API Costs**: ~$11.50 (assuming $0.005 per chunk)
- **Storage**: +50-100 bytes per chunk for summaries
- **One-time cost** during indexing with ongoing benefits

#### Processing Time Optimization Strategies

**Model Selection Impact on Processing Time:**

| Model Type | Speed | Cost | Quality | Processing Time (2,283 chunks) |
|------------|-------|------|---------|--------------------------------|
| **GPT-4o-mini** | ⚡ Fast | 💰 Low | ✅ Good | ~2-4 hours |
| **GPT-3.5-turbo** | ⚡ Fast | 💰 Low | ✅ Good | ~3-5 hours |
| **Claude-3-haiku** | ⚡ Very Fast | 💰 Very Low | ✅ Good | ~1-3 hours |
| **Llama-3.1-8B (local)** | 🚀 Ultra Fast | 💰 Free | ✅ Good | ~30-60 minutes |
| **Qwen2.5-7B (local)** | 🚀 Ultra Fast | 💰 Free | ✅ Good | ~30-60 minutes |
| **GPT-4** | 🐌 Slow | 💰💰 High | 🎯 Excellent | ~15-20 hours |

**Recommended Optimization Approaches:**

1. **Local Model Deployment** (Fastest)
   ```bash
   # Use existing Ollama setup
   ollama pull llama3.1:8b
   ollama pull qwen2.5:7b
   ```
   - **Pros**: No API costs, very fast, unlimited usage
   - **Cons**: Requires GPU/CPU resources, initial setup
   - **Best for**: Large codebases, frequent reprocessing

2. **Batch Processing with Fast API Models**
   ```python
   # Process in parallel batches
   async def process_chunks_batch(chunks, batch_size=10):
       tasks = []
       for i in range(0, len(chunks), batch_size):
           batch = chunks[i:i+batch_size]
           tasks.append(process_batch_async(batch))
       return await asyncio.gather(*tasks)
   ```
   - **Pros**: Significant speedup (5-10x), reasonable costs
   - **Cons**: API rate limits, network dependency
   - **Best for**: One-time processing, moderate codebases

3. **Hybrid Approach** (Recommended)
   ```python
   # Use local model for bulk processing, API for quality checks
   def hybrid_processing(chunks):
       # 90% with local fast model
       bulk_summaries = process_with_local_model(chunks[:2000])
       # 10% with high-quality API model for validation
       quality_samples = process_with_gpt4(chunks[2000:])
       return combine_and_validate(bulk_summaries, quality_samples)
   ```

4. **Progressive Enhancement**
   - **Phase 1**: Fast local model for all chunks (~1 hour)
   - **Phase 2**: API model for critical/complex chunks only
   - **Phase 3**: Iterative improvement based on RAG performance

**Parallel Processing Optimizations:**

```python
# Example implementation for maximum speed
async def optimize_chunk_processing():
    # Use local Ollama for speed
    local_client = OllamaClient("llama3.1:8b")

    # Process in parallel batches
    semaphore = asyncio.Semaphore(8)  # Limit concurrent requests

    async def process_chunk(chunk):
        async with semaphore:
            return await local_client.summarize(chunk)

    # Process all chunks in parallel
    tasks = [process_chunk(chunk) for chunk in chunks]
    summaries = await asyncio.gather(*tasks)

    # Expected time: 30-60 minutes for 2,283 chunks
```

**Quality vs Speed Trade-offs:**

- **Ultra Fast (30-60 min)**: Local Llama/Qwen models - Good quality, perfect for initial implementation
- **Fast (2-4 hours)**: GPT-4o-mini/Claude-haiku - Better quality, reasonable cost
- **Balanced (5-8 hours)**: Hybrid approach - Best quality/speed ratio
- **High Quality (15+ hours)**: GPT-4 only - Excellent quality, high cost

**Hardware Requirements Analysis:**

| Approach | CPU Requirements | RAM Requirements | GPU Requirements | Storage | Network |
|----------|------------------|------------------|------------------|---------|---------|
| **API-Only** | 2-4 cores | 4-8 GB | None | Minimal | High bandwidth |
| **Local 7B-8B Models** | 8+ cores | 16-32 GB | 8+ GB VRAM (recommended) | 10-15 GB | Low |
| **Local 13B+ Models** | 12+ cores | 32-64 GB | 16+ GB VRAM (required) | 20-30 GB | Low |
| **Hybrid Approach** | 6+ cores | 12-24 GB | 6+ GB VRAM | 10-15 GB | Medium |

**Detailed Hardware Considerations:**

**For Local Model Processing (Recommended):**
```bash
# Minimum viable setup
CPU: 8 cores (Intel i7/AMD Ryzen 7)
RAM: 16 GB (24 GB recommended)
GPU: 8 GB VRAM (RTX 3070/4060 Ti, RTX A4000)
Storage: 15 GB free space (SSD recommended)

# Optimal setup for fast processing
CPU: 12+ cores (Intel i9/AMD Ryzen 9)
RAM: 32 GB
GPU: 16+ GB VRAM (RTX 4080/4090, RTX A5000/A6000)
Storage: 50 GB free space (NVMe SSD)
```

**Performance Scaling by Hardware:**

| Hardware Tier | Model Size | Processing Speed | Total Time (2,283 chunks) |
|---------------|------------|------------------|---------------------------|
| **Basic** (8GB VRAM) | Llama-3.1-8B | ~2-3 chunks/min | 60-90 minutes |
| **Good** (12GB VRAM) | Llama-3.1-8B | ~4-5 chunks/min | 30-45 minutes |
| **Excellent** (16GB+ VRAM) | Llama-3.1-8B | ~6-8 chunks/min | 20-30 minutes |
| **Excellent** (16GB+ VRAM) | Llama-3.1-70B | ~1-2 chunks/min | 90-120 minutes |

**CPU-Only Processing (No GPU):**
```bash
# Fallback option if no suitable GPU
Model: Llama-3.1-8B (CPU-only)
Requirements: 16+ cores, 32+ GB RAM
Processing Speed: ~0.5-1 chunks/min
Total Time: 3-6 hours (still faster than API sequential)
```

**Docker Container Resource Allocation:**
```yaml
# docker-compose.yml optimization for local LLM
services:
  ollama:
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 24G
          cpus: '8'
```

**Memory Usage Patterns:**
- **Model Loading**: 8B model = ~8-12 GB VRAM/RAM
- **Inference**: **** GB per concurrent request
- **Batch Processing**: Scale linearly with batch size
- **Peak Usage**: Model size + (batch_size × 2-4 GB)

**Storage Requirements:**
```bash
# Model storage needs
Llama-3.1-8B: ~8 GB
Qwen2.5-7B: ~7 GB
Code-specific models: ~5-15 GB
Ollama cache: ~5-10 GB
Total recommended: 50+ GB free space
```

**Network Considerations:**
- **Local Processing**: Minimal network usage
- **API Processing**: ~1-5 KB per request × 2,283 = 2-11 MB total
- **Hybrid**: Moderate usage for API validation subset
- **Model Downloads**: One-time 8-15 GB download per model

#### Ollama Model Recommendations for RAG Pipeline

Based on your available models, here are the optimal choices for each RAG stage:

**🎯 RECOMMENDED MODEL SELECTION:**

| RAG Stage | Primary Choice | Alternative | Rationale |
|-----------|---------------|-------------|-----------|
| **Embeddings** | `nomic-embed-text:latest` | *(none needed)* | ✅ Already configured, optimized for code |
| **Chunk Summarization** | `deepseek-coder:6.7b` | `codellama:7b-instruct` | 🚀 Fast, code-specialized, good balance |
| **RAG Inference** | `deepseek-coder:33b` | `codestral:22b` | 🎯 Highest quality for final responses |
| **Fast Prototyping** | `smollm2:1.7b` | `deepseek-r1:8b` | ⚡ Ultra-fast for testing concepts |

**DETAILED MODEL ANALYSIS:**

**For Chunk Summarization (Priority: Speed + Code Understanding):**
```bash
# Recommended: deepseek-coder:6.7b
ollama run deepseek-coder:6.7b
```
- **Speed**: ~3-5 chunks/minute (excellent)
- **Quality**: Very good for code summarization
- **VRAM**: ~8-10 GB
- **Specialization**: Trained specifically on code
- **Expected Processing Time**: 30-45 minutes for 2,283 chunks

**Alternative for Chunk Summarization:**
```bash
# Alternative: codellama:7b-instruct
ollama run codellama:7b-instruct
```
- **Speed**: ~2-4 chunks/minute (good)
- **Quality**: Good, instruction-tuned
- **VRAM**: ~8-10 GB
- **Specialization**: Code-focused with instruction following

**For RAG Inference (Priority: Quality + Code Understanding):**
```bash
# Recommended: deepseek-coder:33b
ollama run deepseek-coder:33b
```
- **Speed**: ~1-2 responses/minute (acceptable for final inference)
- **Quality**: Excellent code understanding and explanation
- **VRAM**: ~20-24 GB (requires high-end GPU)
- **Use Case**: Final response generation with RAG context

**Alternative for RAG Inference:**
```bash
# Alternative: codestral:22b
ollama run codestral:22b
```
- **Speed**: ~1-3 responses/minute
- **Quality**: Very good, Mistral-based
- **VRAM**: ~16-18 GB
- **Use Case**: Good balance of quality and resource usage

**For Fast Prototyping/Testing:**
```bash
# Ultra-fast testing: smollm2:1.7b
ollama run smollm2:1.7b
```
- **Speed**: ~10-15 chunks/minute (ultra-fast)
- **Quality**: Basic but sufficient for testing pipeline
- **VRAM**: ~2-3 GB
- **Use Case**: Rapid prototyping and concept validation

**PIPELINE CONFIGURATION EXAMPLES:**

**Option 1: Balanced Performance (Recommended)**
```python
# Chunk summarization
summarization_model = "deepseek-coder:6.7b"
# RAG inference
inference_model = "deepseek-coder:33b"
# Embeddings (already configured)
embedding_model = "nomic-embed-text:latest"
```

**Option 2: Resource-Constrained Setup**
```python
# Use same model for both stages
unified_model = "deepseek-coder:6.7b"  # or codellama:7b-instruct
# Embeddings
embedding_model = "nomic-embed-text:latest"
```

**Option 3: Maximum Quality Setup**
```python
# Chunk summarization
summarization_model = "deepseek-coder:33b"
# RAG inference
inference_model = "deepseek-coder:33b"  # or codestral:22b
# Embeddings
embedding_model = "nomic-embed-text:latest"
```

#### Tesla M40 24GB Specific Considerations

**⚠️ IMPORTANT: Tesla M40 Limitations**
- **Architecture**: Maxwell (2015) - lacks modern tensor cores
- **CUDA Compute**: 5.2 (older, limited FP16 support)
- **Memory Bandwidth**: ~288 GB/s (much slower than modern GPUs)
- **Performance**: ~2-5x slower than modern GPUs for LLM inference
- **Compatibility**: Some newer model formats may not be optimized

**REVISED MODEL RECOMMENDATIONS FOR TESLA M40:**

| Model | Feasibility | Expected Performance | Recommendation |
|-------|-------------|---------------------|----------------|
| `deepseek-coder:33b` | ❌ **Not Recommended** | Too slow (~20-30 min/chunk) | Skip |
| `codestral:22b` | ⚠️ **Marginal** | Very slow (~15-20 min/chunk) | Avoid |
| `deepseek-coder:6.7b` | ✅ **Workable** | Slow (~5-10 min/chunk) | Possible but slow |
| `codellama:7b-instruct` | ✅ **Better Choice** | Moderate (~3-5 min/chunk) | Recommended |
| `qwen3:14b` | ⚠️ **Risky** | Slow (~8-12 min/chunk) | Test carefully |
| `smollm2:1.7b` | ✅ **Best for M40** | Fast (~1-2 min/chunk) | **Primary choice** |
| `deepseek-r1:8b` | ✅ **Good Option** | Moderate (~3-4 min/chunk) | Good alternative |

**OPTIMIZED IMPLEMENTATION STRATEGY FOR TESLA M40:**

**Option 1: Practical Approach (Recommended)**
```python
# Primary: Use smallest effective model
summarization_model = "smollm2:1.7b"
inference_model = "smollm2:1.7b"  # Same model for consistency
embedding_model = "nomic-embed-text:latest"  # Keep current

# Expected processing time: 2-4 hours for 2,283 chunks
# Still much faster than API calls (19+ hours)
```

**Option 2: Quality-Focused (If Time Permits)**
```python
# Use medium model for better quality
summarization_model = "codellama:7b-instruct"
inference_model = "deepseek-r1:8b"
embedding_model = "nomic-embed-text:latest"

# Expected processing time: 8-12 hours for 2,283 chunks
# Better quality but much slower
```

**Option 3: Hybrid CPU/GPU Approach**
```python
# Use CPU for bulk processing, GPU for critical chunks
def hybrid_m40_processing():
    # 80% on CPU (slower but reliable)
    cpu_chunks = process_cpu_only(chunks[:1800])

    # 20% on GPU (faster, for important chunks)
    gpu_chunks = process_gpu_m40(chunks[1800:])

    return combine_results(cpu_chunks, gpu_chunks)
```

**TESLA M40 OPTIMIZATION SETTINGS:**

```bash
# Ollama environment variables for M40
export OLLAMA_GPU_LAYERS=20  # Reduce from default
export OLLAMA_CUDA_MAX_MEMORY=20000000000  # 20GB limit
export OLLAMA_NUM_PARALLEL=1  # Single request at a time
export OLLAMA_FLASH_ATTENTION=false  # Disable if causing issues

# Docker compose override for M40
services:
  ollama:
    environment:
      - OLLAMA_GPU_LAYERS=20
      - OLLAMA_CUDA_MAX_MEMORY=20000000000
      - OLLAMA_NUM_PARALLEL=1
```

**PERFORMANCE EXPECTATIONS:**
- **smollm2:1.7b**: ~1-2 chunks/minute = 2-4 hours total ✅
- **codellama:7b**: ~0.3-0.5 chunks/minute = 8-12 hours total ⚠️
- **deepseek-coder:6.7b**: ~0.2-0.3 chunks/minute = 12-18 hours total ❌

#### 🚀 GAME CHANGER: 2x RTX 3090 Network Access

**HARDWARE UPGRADE OPPORTUNITY:**
- **2x RTX 3090**: 24GB each = 48GB total VRAM
- **Architecture**: Ampere (2020) with tensor cores
- **Performance**: ~10-20x faster than Tesla M40 for LLM inference
- **Current Use**: Salad workloads (revenue generating)

**DISTRIBUTED PROCESSING STRATEGIES:**

**Option 1: Temporary Ollama Migration (Recommended)**
```bash
# Temporarily migrate Ollama to RTX 3090 machine
# Process chunks during off-peak Salad hours
# Return to Salad workloads after processing

# Setup on RTX 3090 machine:
docker run -d --gpus all -p 11434:11434 \
  -v ollama_data:/root/.ollama \
  -e OLLAMA_GPU_LAYERS=60 \
  -e OLLAMA_CUDA_MAX_MEMORY=22000000000 \
  ollama/ollama:latest

# Pull optimal models:
ollama pull deepseek-coder:33b    # Now feasible!
ollama pull codestral:22b         # Backup option
ollama pull nomic-embed-text      # For embeddings
```

**Option 2: Dual-GPU Model Splitting**
```bash
# Use both RTX 3090s for single large model
# Enables 70B+ models across 48GB VRAM
ollama pull deepseek-coder:33b
# Or even larger models like:
# ollama pull codellama:70b-instruct  # If available
```

**Option 3: Parallel Processing Setup**
```python
# Run different models on each GPU simultaneously
# GPU 0: Chunk summarization
# GPU 1: Quality validation/refinement

async def dual_gpu_processing():
    # GPU 0 - Fast summarization
    gpu0_client = OllamaClient("http://rtx3090-1:11434")

    # GPU 1 - Quality enhancement
    gpu1_client = OllamaClient("http://rtx3090-2:11434")

    # Process in parallel
    tasks = []
    for chunk in chunks:
        if chunk.priority == "high":
            tasks.append(gpu1_client.process_high_quality(chunk))
        else:
            tasks.append(gpu0_client.process_fast(chunk))

    return await asyncio.gather(*tasks)
```

**REVISED PERFORMANCE EXPECTATIONS WITH RTX 3090:**

| Model | RTX 3090 Performance | Processing Time (2,283 chunks) | Quality |
|-------|---------------------|--------------------------------|---------|
| `deepseek-coder:33b` | ✅ **8-12 chunks/min** | **20-30 minutes** | 🎯 Excellent |
| `codestral:22b` | ✅ **10-15 chunks/min** | **15-25 minutes** | 🎯 Very Good |
| `deepseek-coder:6.7b` | ✅ **15-20 chunks/min** | **10-15 minutes** | ✅ Good |
| `codellama:7b-instruct` | ✅ **12-18 chunks/min** | **12-18 minutes** | ✅ Good |

**REVENUE IMPACT ANALYSIS:**

```python
# Calculate opportunity cost vs benefits
salad_revenue_per_hour = 2.50  # Example rate
processing_time_hours = 0.5    # 30 minutes with RTX 3090
opportunity_cost = salad_revenue_per_hour * processing_time_hours
# = $1.25 opportunity cost

# vs API costs for same processing:
api_cost_estimate = 2283 * 0.005  # $11.50
time_savings = 19 - 0.5  # 18.5 hours saved

# ROI: Save $10.25 + 18.5 hours for $1.25 opportunity cost
```

**IMPLEMENTATION STRATEGIES:**

**Strategy 1: Off-Peak Processing**
```bash
# Schedule during low Salad demand periods
# Typically: 2-6 AM local time
crontab -e
0 2 * * * /path/to/chunk_processing_script.sh
```

**Strategy 2: Revenue-Aware Scheduling**
```python
def smart_scheduling():
    current_salad_rate = get_current_salad_rate()

    if current_salad_rate < 2.00:  # Low revenue period
        return "process_chunks_now"
    elif current_salad_rate < 3.00:  # Medium revenue
        return "process_critical_chunks_only"
    else:  # High revenue period
        return "defer_to_off_peak"
```

**Strategy 3: Hybrid Revenue Model**
```python
# Reserve 1 GPU for Salad, 1 for LLM processing
# Reduces Salad revenue by 50% but enables continuous processing
# Good for regular/ongoing chunk processing needs
```

**OPTIMAL IMPLEMENTATION PLAN:**

1. **Phase 1**: Test migration during off-peak hours
   - Move Ollama to RTX 3090 temporarily
   - Process 100 chunks with `deepseek-coder:33b`
   - Measure quality improvement vs time cost

2. **Phase 2**: Full processing run
   - Schedule 30-minute window during low Salad rates
   - Process all 2,283 chunks with optimal model
   - Return to Salad workloads immediately after

3. **Phase 3**: Evaluate ongoing strategy
   - If RAG improvement is significant, consider regular processing
   - Implement revenue-aware scheduling
   - Possibly dedicate 1 GPU to LLM tasks permanently

**RECOMMENDED IMMEDIATE ACTION:**
1. **Tonight/Off-Peak**: Temporarily migrate Ollama to RTX 3090
2. **Test Run**: Process 100 chunks with `deepseek-coder:33b`
3. **Measure Impact**: Check RAG performance improvement
4. **Scale Decision**: Based on results, plan full processing run
5. **Revenue Optimization**: Return to Salad or implement hybrid model

This setup could give you **enterprise-grade LLM processing capabilities** while maintaining your revenue stream!

**Recommended Implementation Sequence:**
1. **Assess Current Hardware**: Check GPU VRAM, RAM, and CPU cores
2. **Start with deepseek-coder:6.7b** for chunk summarization prototyping
3. **Benchmark on subset**: Process 100 chunks to estimate full runtime
4. **Scale based on results**: Upgrade to 33B model if hardware allows
5. **Measure RAG performance improvement** with code-specialized summaries
6. **Optimize model selection** based on performance bottlenecks identified

#### Success Metrics
- Measure improvement on existing test query suite
- Target categories: Memory Management, Timer Management, Error Handling
- Expected query success rate improvement: +15-25%

---

### 2. Standalone Comment Block Extraction
**Status**: 📋 Planned  
**Priority**: Medium  
**Estimated Impact**: +5-10% for architectural queries

#### Description
Extract standalone comment blocks, file headers, and API documentation as separate searchable chunks in addition to the current code-embedded approach.

#### Current State
- Comments are **counted and analyzed** but not extracted as separate chunks
- Comments are included within function/class chunks but not independently searchable
- File header comments are filtered out during header processing

#### Proposed Enhancement
Extract as separate chunks:
1. **File Header Comments** - Overall file/module documentation
2. **API Documentation Comments** - Function/class documentation blocks
3. **Large Comment Blocks** - Design explanations, algorithm descriptions
4. **Inline Documentation** - Significant explanatory comments

#### Implementation Approach
```python
def extract_comment_blocks(source_code, filepath, language):
    """Extract standalone comment blocks as separate chunks"""
    comment_chunks = []
    
    # Extract file header comments
    header_comments = extract_file_header_comments(source_code, language)
    
    # Extract function/class documentation
    api_docs = extract_api_documentation(source_code, language)
    
    # Extract large comment blocks
    block_comments = extract_large_comment_blocks(source_code, language)
    
    return comment_chunks
```

#### Expected Benefits
- **Architectural Understanding**: Better responses to "What are the main modules?" queries
- **Design Pattern Recognition**: Improved understanding of implementation rationale
- **API Documentation**: Better context for "how to use X" queries
- **Cross-Reference**: Links between documentation and implementation

#### Target Query Types
- `"What are the main modules in this codebase?"`
- `"What is the overall architecture philosophy?"`
- `"Show me API documentation for memory management"`
- `"What are the design principles behind TMW library?"`

#### Considerations
- **Risk of Noise**: Could dilute high-quality code matches
- **Current Query Optimization**: 85% of current queries target specific implementations
- **Diminishing Returns**: Current system already achieving 60% context utilization

#### Recommendation
- **Implement AFTER** LLM summaries to avoid diluting current performance
- **A/B Test** impact on existing query suite before full deployment
- **Consider** only if adding more architectural/documentation-focused queries

---

## 📊 Performance Tracking

### Current RAG Metrics (Baseline)
- **Avg Chunks Retrieved**: ~10
- **Avg Similarity Score**: ~0.254
- **Context Utilization**: ~60%
- **RAG Retrieval Speed**: ~0.16s
- **Source Files Accessed**: ~4

### Target Metrics (Post-Implementation)
- **Avg Chunks Retrieved**: 12-15
- **Avg Similarity Score**: 0.4-0.6
- **Context Utilization**: 75-85%
- **RAG Retrieval Speed**: <1s (maintain)
- **Source Files Accessed**: 6-8

---

## 🔄 Implementation Sequence

1. **Phase 1**: Implement LLM-generated summaries (Option 2: Enhanced Content)
2. **Phase 2**: Test and measure impact on existing query suite
3. **Phase 3**: Optimize based on results (consider Option 1: Dual Embedding)
4. **Phase 4**: Evaluate comment extraction based on Phase 1-3 results
5. **Phase 5**: Consider additional architectural queries if comment extraction is implemented

---

## 📝 Notes

- Both enhancements target different aspects of the RAG system
- LLM summaries address semantic understanding (primary weakness)
- Comment extraction addresses architectural documentation (secondary need)
- Implementation should be incremental with measurement at each step
- Current test suite provides excellent baseline for measuring improvements

---

*Last Updated: 2025-07-03*
*Based on analysis of test_openwebui_api.py query patterns and current RAG performance metrics*
