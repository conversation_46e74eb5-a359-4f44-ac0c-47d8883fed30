"""
Framework Integration Module
Integrates all components of the Language-Agnostic Core Framework
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from language_framework import CodeAnalysisFramework
from language_registry import create_language_registry, validate_language_coverage
from processing_pipeline import <PERSON>ingPipeline, ProcessingStage, StageResult, StageStatus
from chunk_system import ChunkGeneratorRegistry, create_default_chunk_registry, Chunk

logger = logging.getLogger(__name__)

class CodeAnalysisStage(ProcessingStage):
    """Stage for basic code analysis using language processors"""
    
    def __init__(self, framework: CodeAnalysisFramework):
        self.framework = framework
    
    def get_stage_name(self) -> str:
        return "code_analysis"
    
    def get_dependencies(self) -> List[str]:
        return []  # No dependencies - this is the first stage
    
    def get_stage_description(self) -> str:
        return "Analyze code files using language-specific processors"
    
    def get_required_input_keys(self) -> List[str]:
        return ["file_paths"]
    
    async def process(self, input_data: Any, context: Dict[str, Any]) -> Any:
        """Process code files and extract language-specific contexts"""
        file_paths = input_data.get("file_paths", [])
        
        if not file_paths:
            raise ValueError("No file paths provided for code analysis")
        
        # Analyze file relationships
        relationships = self.framework.analyze_file_relationships(file_paths)
        
        # Extract contexts
        contexts = self.framework.extract_contexts(file_paths)
        
        # Generate architectural insights
        insights = {}
        for language in self.framework.get_supported_languages():
            language_contexts = [ctx for ctx in contexts.values() if ctx.language == language]
            if language_contexts:
                processor = self.framework.language_processors[language]
                language_insights = processor.generate_architectural_insights(language_contexts)
                insights[language] = language_insights
        
        return {
            "file_relationships": relationships,
            "language_contexts": contexts,
            "architectural_insights": insights,
            "processed_files": len(file_paths),
            "supported_languages": list(insights.keys())
        }

class ChunkGenerationStage(ProcessingStage):
    """Stage for generating chunks from analyzed code"""
    
    def __init__(self, chunk_registry: ChunkGeneratorRegistry, llm_client: Any):
        self.chunk_registry = chunk_registry
        self.llm_client = llm_client
    
    def get_stage_name(self) -> str:
        return "chunk_generation"
    
    def get_dependencies(self) -> List[str]:
        return ["code_analysis"]
    
    def get_stage_description(self) -> str:
        return "Generate searchable chunks from analyzed code"
    
    async def process(self, input_data: Any, context: Dict[str, Any]) -> Any:
        """Generate chunks from code analysis results"""
        code_analysis = input_data["code_analysis"]
        language_contexts = code_analysis["language_contexts"]
        
        chunks = []
        
        # Generate code implementation chunks
        for file_path, lang_context in language_contexts.items():
            chunk_context = {
                "code_content": lang_context.content,
                "file_path": lang_context.file_path,
                "language": lang_context.language
            }
            
            try:
                chunk = await self.chunk_registry.generate_chunk(
                    "code_implementation", chunk_context, self.llm_client
                )
                chunks.append(chunk)
            except Exception as e:
                logger.error(f"Failed to generate chunk for {file_path}: {e}")
        
        # Generate architectural chunks
        architectural_insights = code_analysis["architectural_insights"]
        for language, insights in architectural_insights.items():
            if insights:
                chunk_context = {
                    "architectural_analysis": insights,
                    "source_files": [ctx.file_path for ctx in language_contexts.values() if ctx.language == language]
                }
                
                try:
                    chunk = await self.chunk_registry.generate_chunk(
                        "architectural_pattern", chunk_context, self.llm_client
                    )
                    chunks.append(chunk)
                except Exception as e:
                    logger.error(f"Failed to generate architectural chunk for {language}: {e}")
        
        return {
            "generated_chunks": chunks,
            "chunk_count": len(chunks),
            "chunk_types": list(set(chunk.metadata.chunk_type for chunk in chunks))
        }

class IntegratedCodeAnalysisSystem:
    """Integrated system combining all framework components"""
    
    def __init__(self, llm_client: Any = None):
        self.framework = create_language_registry()
        self.chunk_registry = create_default_chunk_registry()
        self.pipeline = ProcessingPipeline("integrated_analysis")
        self.llm_client = llm_client or self._create_mock_llm_client()
        
        # Register processing stages
        self._setup_pipeline()
        
        logger.info("Initialized integrated code analysis system")
    
    def _create_mock_llm_client(self):
        """Create a mock LLM client for testing"""
        class MockLLMClient:
            async def generate(self, prompt):
                return f"Mock analysis for prompt: {prompt[:100]}..."
        
        return MockLLMClient()
    
    def _setup_pipeline(self):
        """Setup the processing pipeline with stages"""
        # Register core stages
        code_analysis_stage = CodeAnalysisStage(self.framework)
        chunk_generation_stage = ChunkGenerationStage(self.chunk_registry, self.llm_client)
        
        self.pipeline.register_stage(code_analysis_stage)
        self.pipeline.register_stage(chunk_generation_stage)
    
    async def analyze_codebase(self, codebase_path: str, file_patterns: List[str] = None) -> Dict[str, Any]:
        """Analyze an entire codebase"""
        if file_patterns is None:
            file_patterns = ["**/*"]
        
        # Discover files
        file_paths = self._discover_files(codebase_path, file_patterns)
        
        if not file_paths:
            raise ValueError(f"No supported files found in {codebase_path}")
        
        logger.info(f"Analyzing {len(file_paths)} files from {codebase_path}")
        
        # Execute pipeline
        input_data = {"file_paths": file_paths}
        results = await self.pipeline.execute_pipeline(input_data)
        
        # Compile final results
        analysis_result = {
            "codebase_path": codebase_path,
            "total_files": len(file_paths),
            "pipeline_results": results,
            "success": all(result.status == StageStatus.COMPLETED for result in results.values()),
            "processing_summary": self._create_processing_summary(results)
        }
        
        return analysis_result
    
    def _discover_files(self, codebase_path: str, patterns: List[str]) -> List[str]:
        """Discover files matching patterns in codebase"""
        codebase_path = Path(codebase_path)
        supported_extensions = self.framework.get_supported_extensions()
        
        discovered_files = []
        
        for pattern in patterns:
            for file_path in codebase_path.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    discovered_files.append(str(file_path))
        
        return discovered_files
    
    def _create_processing_summary(self, results: Dict[str, StageResult]) -> Dict[str, Any]:
        """Create summary of processing results"""
        summary = {
            "stages_completed": sum(1 for result in results.values() if result.status == StageStatus.COMPLETED),
            "stages_failed": sum(1 for result in results.values() if result.status == StageStatus.FAILED),
            "total_processing_time": sum(result.execution_time for result in results.values()),
            "stage_details": {}
        }
        
        for stage_name, result in results.items():
            summary["stage_details"][stage_name] = {
                "status": result.status.value,
                "execution_time": result.execution_time,
                "error_message": result.error_message
            }
        
        return summary
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get information about the integrated system"""
        return {
            "framework_info": {
                "supported_languages": self.framework.get_supported_languages(),
                "supported_extensions": list(self.framework.get_supported_extensions()),
                "total_processors": len(self.framework.language_processors)
            },
            "chunk_registry_info": self.chunk_registry.get_registry_info(),
            "pipeline_info": self.pipeline.get_pipeline_info(),
            "system_status": "ready"
        }
    
    def validate_system(self) -> Dict[str, Any]:
        """Validate the entire system configuration"""
        validation_results = {
            "language_coverage": validate_language_coverage(),
            "pipeline_validation": self.pipeline.validate_pipeline(),
            "chunk_registry_status": {
                "registered_types": len(self.chunk_registry.generators),
                "available_types": self.chunk_registry.get_registered_types()
            }
        }
        
        # Overall system validation
        validation_results["system_valid"] = (
            validation_results["language_coverage"]["coverage_complete"] and
            validation_results["pipeline_validation"]["valid"] and
            validation_results["chunk_registry_status"]["registered_types"] > 0
        )
        
        return validation_results
    
    async def process_query(self, query: str, codebase_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process a query using the integrated system"""
        # Route query using framework's query intelligence
        routing_info = self.framework.route_query(query)
        
        # This would integrate with the actual RAG system
        # For now, return routing information
        return {
            "query": query,
            "routing_info": routing_info,
            "recommended_processors": routing_info["recommended_processors"],
            "processing_strategy": routing_info["processing_strategy"],
            "system_ready": True
        }

async def main():
    """Main function to demonstrate the integrated system"""
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 Initializing Integrated Code Analysis System...")
    system = IntegratedCodeAnalysisSystem()
    
    print("\n📊 System Information:")
    info = system.get_system_info()
    print(f"Supported Languages: {len(info['framework_info']['supported_languages'])}")
    print(f"Supported Extensions: {len(info['framework_info']['supported_extensions'])}")
    print(f"Registered Chunk Types: {len(info['chunk_registry_info']['registered_types'])}")
    print(f"Pipeline Stages: {info['pipeline_info']['total_stages']}")
    
    print("\n✅ Validating System...")
    validation = system.validate_system()
    print(f"System Valid: {validation['system_valid']}")
    print(f"Language Coverage Complete: {validation['language_coverage']['coverage_complete']}")
    print(f"Pipeline Valid: {validation['pipeline_validation']['valid']}")
    
    if validation['language_coverage']['missing_languages']:
        print(f"Missing Languages: {validation['language_coverage']['missing_languages']}")
    
    print("\n🔍 Testing Query Processing...")
    test_queries = [
        "How does the authentication system work?",
        "Find all Python functions that handle errors",
        "What design patterns are used in this codebase?",
        "Show me the C++ memory management code"
    ]
    
    for query in test_queries:
        result = await system.process_query(query)
        print(f"Query: {query}")
        print(f"  Strategy: {result['processing_strategy']}")
        print(f"  Recommended: {result['recommended_processors'][:3]}...")  # Show first 3
        print()
    
    print("✨ System initialization and validation complete!")

if __name__ == "__main__":
    asyncio.run(main())
