{"data_mtime": 1751504572, "dep_lines": [36, 36, 37, 38, 51, 52, 61, 96, 98, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 34, 36, 64, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 25, 25, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 10, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aiohttp.hdrs", "aiohttp.helpers", "aiohttp.abc", "aiohttp.client_exceptions", "aiohttp.client_proto", "aiohttp.client_reqrep", "aiohttp.resolver", "aiohttp.client", "aiohttp.tracing", "asyncio", "functools", "random", "socket", "sys", "traceback", "warnings", "collections", "contextlib", "http", "itertools", "time", "types", "typing", "aiohappyeyeballs", "aiohttp", "ssl", "builtins", "_asyncio", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "abc", "aiohappyeyeballs.utils", "aiohttp.base_protocol", "aiohttp.http_writer", "aiohttp.streams", "asyncio.base_events", "asyncio.events", "asyncio.proactor_events", "asyncio.protocols", "asyncio.transports", "asyncio.windows_events", "enum", "http.cookies", "multidict", "yarl", "yarl._url"], "hash": "20d782ba5caa088f40fbee60a0e8bf09cfb440e6", "id": "aiohttp.connector", "ignore_all": true, "interface_hash": "762e25b359bd194cedc420ec24e470a6d24efdf3", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\connector.py", "plugin_data": null, "size": 61877, "suppressed": [], "version_id": "1.15.0"}