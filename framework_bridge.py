"""
Framework Bridge - Integration between old CodePreprocessor and new Framework
Provides backward compatibility while enabling gradual migration to new architecture.
"""

import os
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

# Import existing code preprocessor
from code_preprocessor import CodePreprocessor

# Import new framework components
from framework_integration import IntegratedCodeAnalysisSystem
from language_registry import create_language_registry
from chunk_system import create_default_chunk_registry

logger = logging.getLogger(__name__)

class FrameworkBridge:
    """
    Bridge between old CodePreprocessor and new Language-Agnostic Framework.
    Provides backward compatibility while enabling new features.
    """
    
    def __init__(self, repo_path: str, use_new_framework: bool = False):
        self.repo_path = repo_path
        self.use_new_framework = use_new_framework
        
        # Initialize old system (always available for compatibility)
        self.old_preprocessor = CodePreprocessor(repo_path)
        
        # Initialize new system (optional)
        self.new_system = None
        if use_new_framework:
            self.new_system = IntegratedCodeAnalysisSystem()
        
        logger.info(f"FrameworkBridge initialized for {repo_path}")
        logger.info(f"Using new framework: {use_new_framework}")
    
    def process_repository(self, exclude_dirs: Optional[List[str]] = None, 
                          use_enhanced: bool = False) -> List[Dict[str, Any]]:
        """
        Process repository using either old or new system.
        
        Args:
            exclude_dirs: Directories to exclude from processing
            use_enhanced: Whether to use new framework features
            
        Returns:
            List of processed chunks compatible with existing vector DB
        """
        
        if use_enhanced and self.new_system:
            return self._process_with_new_framework(exclude_dirs)
        else:
            return self._process_with_old_system(exclude_dirs)
    
    def _process_with_old_system(self, exclude_dirs: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Process using existing CodePreprocessor (backward compatibility)"""
        logger.info("Processing with existing CodePreprocessor system")
        
        # Use existing method
        chunks = self.old_preprocessor.process_repository(exclude_dirs)
        
        logger.info(f"Processed {len(chunks)} chunks using old system")
        return chunks
    
    async def _process_with_new_framework(self, exclude_dirs: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Process using new Language-Agnostic Framework"""
        logger.info("Processing with new Language-Agnostic Framework")
        
        try:
            # Analyze codebase with new system
            result = await self.new_system.analyze_codebase(self.repo_path)
            
            if not result["success"]:
                logger.warning("New framework processing failed, falling back to old system")
                return self._process_with_old_system(exclude_dirs)
            
            # Convert new framework results to old format for compatibility
            chunks = self._convert_new_to_old_format(result)
            
            logger.info(f"Processed {len(chunks)} chunks using new framework")
            return chunks
            
        except Exception as e:
            logger.error(f"New framework processing failed: {e}")
            logger.info("Falling back to old system")
            return self._process_with_old_system(exclude_dirs)
    
    def _convert_new_to_old_format(self, new_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert new framework results to old format for backward compatibility"""
        
        chunks = []
        
        # Extract chunk generation results
        pipeline_results = new_result.get("pipeline_results", {})
        chunk_generation = pipeline_results.get("chunk_generation")
        
        if chunk_generation and chunk_generation.status.value == "completed":
            generated_chunks = chunk_generation.output_data.get("generated_chunks", [])
            
            for new_chunk in generated_chunks:
                # Convert to old format
                old_chunk = {
                    "content": new_chunk.content.primary_content,
                    "metadata": {
                        "filepath": new_chunk.metadata.source_files[0] if new_chunk.metadata.source_files else "",
                        "language": new_chunk.metadata.language,
                        "chunk_type": new_chunk.metadata.chunk_type,
                        "start_line": 1,  # Would need to extract from new system
                        "end_line": 100,  # Would need to extract from new system
                        "function_name": "",  # Would need to extract
                        "class_name": "",  # Would need to extract
                        "complexity": "medium",  # Default
                        "semantic_tags": list(new_chunk.metadata.tags),
                        "quality_score": new_chunk.metadata.quality_score,
                        "processing_time": new_chunk.metadata.processing_time,
                        "enhanced": True  # Mark as processed by new system
                    }
                }
                chunks.append(old_chunk)
        
        return chunks
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages from both systems"""
        old_languages = self._get_old_system_languages()
        
        if self.new_system:
            new_languages = self.new_system.framework.get_supported_languages()
            return list(set(old_languages + new_languages))
        
        return old_languages
    
    def _get_old_system_languages(self) -> List[str]:
        """Extract supported languages from old system"""
        # Based on extensions in code_preprocessor.py
        return [
            "c", "cpp", "python", "csharp", "javascript", "typescript", 
            "rust", "java", "go", "sql", "tcl", "verilog", "bash",
            "lisp", "scheme", "lua", "make", "json", "yaml", "xml",
            "php", "perl", "markdown", "html", "fortran", "vhdl", "toml"
        ]
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get information about both systems"""
        info = {
            "bridge_mode": True,
            "repo_path": self.repo_path,
            "old_system": {
                "available": True,
                "class": "CodePreprocessor",
                "supported_languages": len(self._get_old_system_languages())
            },
            "new_system": {
                "available": self.new_system is not None,
                "class": "IntegratedCodeAnalysisSystem" if self.new_system else None,
                "supported_languages": len(self.new_system.framework.get_supported_languages()) if self.new_system else 0
            }
        }
        
        if self.new_system:
            info["new_system"].update(self.new_system.get_system_info())
        
        return info
    
    def validate_compatibility(self) -> Dict[str, Any]:
        """Validate compatibility between old and new systems"""
        validation = {
            "old_system_working": False,
            "new_system_working": False,
            "language_overlap": 0,
            "compatibility_issues": []
        }
        
        try:
            # Test old system
            test_chunks = self.old_preprocessor.process_repository()
            validation["old_system_working"] = len(test_chunks) > 0
        except Exception as e:
            validation["compatibility_issues"].append(f"Old system error: {e}")
        
        if self.new_system:
            try:
                # Test new system
                validation_result = self.new_system.validate_system()
                validation["new_system_working"] = validation_result["system_valid"]
                
                if not validation_result["system_valid"]:
                    validation["compatibility_issues"].extend([
                        f"New system validation: {issue}" 
                        for issue in validation_result.get("pipeline_validation", {}).get("issues", [])
                    ])
                
                # Check language overlap
                old_langs = set(self._get_old_system_languages())
                new_langs = set(self.new_system.framework.get_supported_languages())
                validation["language_overlap"] = len(old_langs & new_langs)
                
            except Exception as e:
                validation["compatibility_issues"].append(f"New system error: {e}")
        
        return validation

class BackwardCompatibilityAdapter:
    """
    Adapter to make new framework compatible with existing code that uses CodePreprocessor
    """
    
    def __init__(self, repo_path: str):
        self.bridge = FrameworkBridge(repo_path, use_new_framework=True)
    
    def process_repository(self, exclude_dirs=None):
        """Drop-in replacement for CodePreprocessor.process_repository()"""
        return self.bridge.process_repository(exclude_dirs, use_enhanced=False)
    
    async def process_repository_enhanced(self, exclude_dirs=None):
        """Enhanced version using new framework"""
        return await self.bridge._process_with_new_framework(exclude_dirs)

def create_preprocessor_replacement(repo_path: str, enhanced: bool = False):
    """
    Factory function to create preprocessor with optional enhancement
    
    Args:
        repo_path: Path to repository
        enhanced: Whether to use new framework features
        
    Returns:
        Compatible preprocessor instance
    """
    
    if enhanced:
        # Return bridge with new framework
        return FrameworkBridge(repo_path, use_new_framework=True)
    else:
        # Return original preprocessor for full compatibility
        return CodePreprocessor(repo_path)

# Migration utilities

def migrate_existing_code(file_path: str, backup: bool = True):
    """
    Utility to help migrate existing code from CodePreprocessor to new framework
    
    Args:
        file_path: Path to Python file using CodePreprocessor
        backup: Whether to create backup before modification
    """
    
    if backup:
        backup_path = f"{file_path}.backup"
        import shutil
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
    
    # Read file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace imports
    replacements = [
        ("from code_preprocessor import CodePreprocessor", 
         "from framework_bridge import BackwardCompatibilityAdapter as CodePreprocessor"),
        ("import code_preprocessor", 
         "import framework_bridge as code_preprocessor")
    ]
    
    modified = content
    for old, new in replacements:
        if old in modified:
            modified = modified.replace(old, new)
            logger.info(f"Replaced: {old} -> {new}")
    
    # Write modified file
    if modified != content:
        with open(file_path, 'w') as f:
            f.write(modified)
        logger.info(f"Updated {file_path} for framework compatibility")
    else:
        logger.info(f"No changes needed for {file_path}")

if __name__ == "__main__":
    # Test the bridge
    import asyncio
    
    async def test_bridge():
        logging.basicConfig(level=logging.INFO)
        
        # Test with a sample repository path
        repo_path = "."  # Current directory
        
        print("🌉 Testing Framework Bridge...")
        bridge = FrameworkBridge(repo_path, use_new_framework=True)
        
        print("\n📊 System Information:")
        info = bridge.get_system_info()
        print(f"Old system languages: {info['old_system']['supported_languages']}")
        print(f"New system languages: {info['new_system']['supported_languages']}")
        
        print("\n✅ Compatibility Validation:")
        validation = bridge.validate_compatibility()
        print(f"Old system working: {validation['old_system_working']}")
        print(f"New system working: {validation['new_system_working']}")
        print(f"Language overlap: {validation['language_overlap']}")
        
        if validation['compatibility_issues']:
            print("Issues found:")
            for issue in validation['compatibility_issues']:
                print(f"  - {issue}")
        
        print("\n🔄 Testing Backward Compatibility:")
        adapter = BackwardCompatibilityAdapter(repo_path)
        
        # This should work exactly like the old CodePreprocessor
        try:
            chunks = adapter.process_repository()
            print(f"✅ Processed {len(chunks)} chunks with backward compatibility")
        except Exception as e:
            print(f"❌ Backward compatibility test failed: {e}")
    
    asyncio.run(test_bridge())
