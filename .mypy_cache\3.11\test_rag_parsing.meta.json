{"data_mtime": 1751507132, "dep_lines": [4, 5, 6, 11, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["asyncio", "sys", "os", "test_openwebui_api", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "973a9656f9e94087cdc77beab6aac68c66855c45", "id": "test_rag_parsing", "ignore_all": false, "interface_hash": "b53bcb5981f045561ad7f6e72fe8298365fddb31", "mtime": 1751507130, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_rag_parsing.py", "plugin_data": null, "size": 966, "suppressed": [], "version_id": "1.15.0"}