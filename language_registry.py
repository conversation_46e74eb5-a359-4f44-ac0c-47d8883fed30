"""
Language Processor Registry for 27 Supported Languages
Implements registration and management of all language processors.
"""

from typing import Dict, List, Set, Any
from language_framework import CodeAnalysisFramework, LanguageProcessor
from language_processors import CCppProcessor, PythonProcessor, CSharpProcessor, JavaScriptProcessor
from semantic_patterns import semantic_registry
import logging

logger = logging.getLogger(__name__)

class GenericLanguageProcessor(LanguageProcessor):
    """Generic processor for languages without specialized processing"""
    
    def __init__(self, language_name: str, extensions: Set[str], priority: int = 5):
        self.language_name = language_name
        self.extensions = extensions
        self.priority = priority
    
    def get_language_name(self) -> str:
        return self.language_name
    
    def get_supported_extensions(self) -> Set[str]:
        return self.extensions
    
    def get_processing_priority(self) -> int:
        return self.priority
    
    def detect_file_relationships(self, file_path: str) -> List:
        """Basic file relationship detection"""
        return []  # Generic processor doesn't detect relationships
    
    def extract_context(self, file_path: str, content: str):
        """Enhanced context extraction using semantic patterns"""
        from language_framework import LanguageContext, AnalysisScope

        # Use semantic patterns for this language
        semantic_elements = semantic_registry.extract_semantic_elements(content, self.language_name)

        # Enhanced metadata extraction
        metadata = {
            "line_count": len(content.splitlines()),
            "char_count": len(content),
            "has_comments": self._has_comments(content),
            "estimated_complexity": self._estimate_complexity(content),
            "functions": semantic_elements.get("function_patterns", []),
            "classes": semantic_elements.get("class_patterns", []),
            "imports": semantic_elements.get("import_patterns", []),
            "variables": semantic_elements.get("variable_patterns", []),
            "semantic_complexity": self._calculate_semantic_complexity(semantic_elements),
            "language_features": self._identify_language_features(content, semantic_elements)
        }

        return LanguageContext(
            language=self.language_name,
            file_path=file_path,
            content=content,
            relationships=[],
            metadata=metadata
        )
    
    def generate_architectural_insights(self, contexts: List) -> Dict:
        """Basic architectural insights"""
        insights = {
            "file_count": len(contexts),
            "total_lines": sum(ctx.metadata.get("line_count", 0) for ctx in contexts if ctx.language == self.language_name),
            "language": self.language_name
        }
        return insights
    
    def _has_comments(self, content: str) -> bool:
        """Check if content has comments (basic detection)"""
        comment_indicators = ['//', '/*', '#', '--', '<!--']
        return any(indicator in content for indicator in comment_indicators)
    
    def _estimate_complexity(self, content: str) -> str:
        """Estimate code complexity based on content length"""
        lines = len(content.splitlines())
        if lines < 50:
            return "low"
        elif lines < 200:
            return "medium"
        else:
            return "high"

    def _calculate_semantic_complexity(self, semantic_elements: Dict[str, List[str]]) -> str:
        """Calculate semantic complexity based on extracted elements"""
        total_elements = sum(len(elements) for elements in semantic_elements.values())

        if total_elements < 5:
            return "low"
        elif total_elements < 20:
            return "medium"
        else:
            return "high"

    def _identify_language_features(self, content: str, semantic_elements: Dict[str, List[str]]) -> List[str]:
        """Identify language-specific features"""
        features = []

        # Generic feature detection
        if len(semantic_elements.get("function_patterns", [])) > 0:
            features.append("functions")

        if len(semantic_elements.get("class_patterns", [])) > 0:
            features.append("object_oriented")

        if len(semantic_elements.get("import_patterns", [])) > 0:
            features.append("modular")

        # Language-specific feature detection
        if self.language_name == "rust":
            if "unsafe" in content:
                features.append("unsafe_code")
            if "async" in content:
                features.append("async_programming")

        elif self.language_name == "go":
            if "goroutine" in content or "go func" in content:
                features.append("concurrency")
            if "channel" in content:
                features.append("channels")

        elif self.language_name == "java":
            if "synchronized" in content:
                features.append("thread_safety")
            if "@" in content:
                features.append("annotations")

        elif self.language_name == "typescript":
            if "interface" in content:
                features.append("type_definitions")
            if "generic" in content or "<T>" in content:
                features.append("generics")

        return features

def create_language_registry() -> CodeAnalysisFramework:
    """Create and populate the language registry with all 27 supported languages"""
    
    framework = CodeAnalysisFramework()
    
    # Register specialized processors (high priority)
    framework.register_language_processor(CCppProcessor())
    framework.register_language_processor(PythonProcessor())
    framework.register_language_processor(CSharpProcessor())
    framework.register_language_processor(JavaScriptProcessor())
    
    # Register generic processors for remaining languages
    language_configs = [
        # Web Technologies
        ("typescript", {".ts", ".tsx"}, 2),
        ("html", {".html", ".htm"}, 3),
        ("css", {".css"}, 3),
        ("scss", {".scss", ".sass"}, 3),
        ("php", {".php"}, 3),
        
        # Systems Programming
        ("rust", {".rs"}, 2),
        ("go", {".go"}, 2),
        ("swift", {".swift"}, 3),
        ("kotlin", {".kt", ".kts"}, 3),
        
        # JVM Languages
        ("java", {".java"}, 2),
        ("scala", {".scala"}, 3),
        ("groovy", {".groovy"}, 4),
        
        # Functional Languages
        ("haskell", {".hs"}, 4),
        ("erlang", {".erl"}, 4),
        ("elixir", {".ex", ".exs"}, 4),
        ("clojure", {".clj", ".cljs"}, 4),
        ("lisp", {".lisp", ".lsp"}, 4),
        ("scheme", {".scm", ".ss"}, 4),
        
        # Scripting Languages
        ("ruby", {".rb"}, 3),
        ("perl", {".pl", ".pm"}, 4),
        ("lua", {".lua"}, 4),
        ("bash", {".sh", ".bash"}, 3),
        
        # Data and Configuration
        ("sql", {".sql"}, 3),
        ("json", {".json"}, 4),
        ("yaml", {".yaml", ".yml"}, 4),
        ("xml", {".xml"}, 4),
        ("toml", {".toml"}, 4),
        
        # Hardware Description
        ("verilog", {".v", ".vh"}, 4),
        ("vhdl", {".vhd", ".vhdl"}, 4),
        
        # Assembly
        ("assembly", {".asm", ".s"}, 4),
        
        # Scientific Computing
        ("matlab", {".m"}, 4),
        ("r", {".r", ".R"}, 4),
        
        # Build Systems
        ("makefile", {"Makefile", ".mk"}, 4),
        ("cmake", {".cmake"}, 4),
        
        # Documentation
        ("markdown", {".md", ".markdown"}, 4),
        ("tex", {".tex"}, 4),
        
        # Other
        ("fortran", {".f", ".f90", ".f95"}, 4),
        ("pascal", {".pas"}, 4),
        ("tcl", {".tcl"}, 4)
    ]
    
    # Register all generic processors
    for lang_name, extensions, priority in language_configs:
        processor = GenericLanguageProcessor(lang_name, extensions, priority)
        framework.register_language_processor(processor)
    
    logger.info(f"Registered {len(framework.get_supported_languages())} language processors")
    logger.info(f"Supported languages: {', '.join(sorted(framework.get_supported_languages()))}")
    logger.info(f"Supported extensions: {', '.join(sorted(framework.get_supported_extensions()))}")
    
    return framework

def get_language_statistics(framework: CodeAnalysisFramework) -> Dict[str, Any]:
    """Get statistics about registered languages"""
    
    processors = framework.language_processors
    
    stats = {
        "total_languages": len(processors),
        "specialized_processors": [],
        "generic_processors": [],
        "extensions_by_language": {},
        "priority_distribution": {}
    }
    
    for lang_name, processor in processors.items():
        extensions = processor.get_supported_extensions()
        priority = processor.get_processing_priority()
        
        stats["extensions_by_language"][lang_name] = list(extensions)
        
        if priority not in stats["priority_distribution"]:
            stats["priority_distribution"][priority] = []
        stats["priority_distribution"][priority].append(lang_name)
        
        # Classify processor type
        if isinstance(processor, GenericLanguageProcessor):
            stats["generic_processors"].append(lang_name)
        else:
            stats["specialized_processors"].append(lang_name)
    
    return stats

def validate_language_coverage() -> Dict[str, any]:
    """Validate that all 27 languages are properly covered"""
    
    framework = create_language_registry()
    stats = get_language_statistics(framework)
    
    expected_languages = {
        # Core languages (specialized)
        "c_cpp", "python", "csharp", "javascript",
        
        # Additional languages (generic)
        "typescript", "html", "css", "scss", "php",
        "rust", "go", "swift", "kotlin",
        "java", "scala", "groovy",
        "haskell", "erlang", "elixir", "clojure", "lisp", "scheme",
        "ruby", "perl", "lua", "bash",
        "sql", "json", "yaml", "xml", "toml",
        "verilog", "vhdl", "assembly",
        "matlab", "r", "makefile", "cmake",
        "markdown", "tex", "fortran", "pascal", "tcl"
    }
    
    registered_languages = set(framework.get_supported_languages())
    
    validation_result = {
        "expected_count": len(expected_languages),
        "registered_count": len(registered_languages),
        "missing_languages": list(expected_languages - registered_languages),
        "extra_languages": list(registered_languages - expected_languages),
        "coverage_complete": expected_languages == registered_languages,
        "statistics": stats
    }
    
    return validation_result

if __name__ == "__main__":
    # Test the language registry
    logging.basicConfig(level=logging.INFO)
    
    print("Creating language registry...")
    framework = create_language_registry()
    
    print("\nValidating language coverage...")
    validation = validate_language_coverage()
    
    print(f"\nValidation Results:")
    print(f"Expected languages: {validation['expected_count']}")
    print(f"Registered languages: {validation['registered_count']}")
    print(f"Coverage complete: {validation['coverage_complete']}")
    
    if validation['missing_languages']:
        print(f"Missing languages: {validation['missing_languages']}")
    
    if validation['extra_languages']:
        print(f"Extra languages: {validation['extra_languages']}")
    
    print(f"\nSpecialized processors: {validation['statistics']['specialized_processors']}")
    print(f"Generic processors: {len(validation['statistics']['generic_processors'])}")
    
    print(f"\nTotal supported extensions: {len(framework.get_supported_extensions())}")
    
    # Test file processing
    print("\nTesting file type detection...")
    test_files = [
        "test.py", "test.cpp", "test.h", "test.cs", "test.js",
        "test.rs", "test.go", "test.java", "test.sql", "test.md"
    ]
    
    for test_file in test_files:
        processor = framework.get_processor_for_file(test_file)
        if processor:
            print(f"{test_file} -> {processor.get_language_name()}")
        else:
            print(f"{test_file} -> No processor found")
