{"data_mtime": 1751504572, "dep_lines": [8, 25, 26, 27, 28, 29, 30, 91, 92, 93, 97, 102, 107, 113, 129, 140, 141, 154, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 292, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "aiohttp.abc", "aiohttp.helpers", "aiohttp.log", "aiohttp.typedefs", "aiohttp.web_app", "aiohttp.web_exceptions", "aiohttp.web_fileresponse", "aiohttp.web_log", "aiohttp.web_middlewares", "aiohttp.web_protocol", "aiohttp.web_request", "aiohttp.web_response", "aiohttp.web_routedef", "aiohttp.web_runner", "aiohttp.web_server", "aiohttp.web_urldispatcher", "aiohttp.web_ws", "asyncio", "logging", "os", "socket", "sys", "warnings", "<PERSON><PERSON><PERSON><PERSON>", "contextlib", "importlib", "typing", "ssl", "builtins", "_asyncio", "_frozen_importlib", "_socket", "_ssl", "_typeshed", "abc", "asyncio.events"], "hash": "3096b4717bc7e56dc4b36cd1a4891509da0a1990", "id": "aiohttp.web", "ignore_all": true, "interface_hash": "5da5375142ab25a5a92f47e1f2c408c3a8127268", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web.py", "plugin_data": null, "size": 19027, "suppressed": [], "version_id": "1.15.0"}