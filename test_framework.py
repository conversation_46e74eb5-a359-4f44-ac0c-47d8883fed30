"""
Comprehensive Test Suite for Language-Agnostic Framework
Tests all components of Phase 0 implementation
"""

import asyncio
import logging
import tempfile
import os
from pathlib import Path
from typing import Dict, Any

from framework_integration import IntegratedCodeAnalysisSystem
from language_registry import create_language_registry, validate_language_coverage
from processing_pipeline import ProcessingPipeline
from chunk_system import create_default_chunk_registry

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FrameworkTester:
    """Comprehensive tester for the framework"""
    
    def __init__(self):
        self.test_results = {}
        self.system = None
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all framework tests"""
        print("🧪 Starting Comprehensive Framework Tests\n")
        
        # Test 1: Language Registry
        await self.test_language_registry()
        
        # Test 2: Processing Pipeline
        await self.test_processing_pipeline()
        
        # Test 3: Chunk System
        await self.test_chunk_system()
        
        # Test 4: Integrated System
        await self.test_integrated_system()
        
        # Test 5: File Processing
        await self.test_file_processing()
        
        # Test 6: Query Processing
        await self.test_query_processing()
        
        # Generate final report
        return self.generate_test_report()
    
    async def test_language_registry(self):
        """Test language registry functionality"""
        print("📋 Testing Language Registry...")
        
        try:
            # Create registry
            framework = create_language_registry()
            
            # Validate coverage
            validation = validate_language_coverage()
            
            # Test file type detection
            test_files = [
                ("test.py", "python"),
                ("test.cpp", "c_cpp"),
                ("test.h", "c_cpp"),
                ("test.cs", "csharp"),
                ("test.js", "javascript"),
                ("test.rs", "rust"),
                ("test.go", "go"),
                ("test.java", "java"),
                ("test.sql", "sql"),
                ("test.md", "markdown")
            ]
            
            detection_results = {}
            for filename, expected_lang in test_files:
                processor = framework.get_processor_for_file(filename)
                detected_lang = processor.get_language_name() if processor else None
                detection_results[filename] = {
                    "expected": expected_lang,
                    "detected": detected_lang,
                    "correct": detected_lang == expected_lang
                }
            
            self.test_results["language_registry"] = {
                "status": "PASSED",
                "total_languages": len(framework.get_supported_languages()),
                "coverage_complete": validation["coverage_complete"],
                "missing_languages": validation["missing_languages"],
                "detection_accuracy": sum(1 for r in detection_results.values() if r["correct"]) / len(detection_results),
                "detection_results": detection_results
            }
            
            print(f"  ✅ Languages: {len(framework.get_supported_languages())}/27")
            print(f"  ✅ Coverage: {'Complete' if validation['coverage_complete'] else 'Incomplete'}")
            print(f"  ✅ Detection: {self.test_results['language_registry']['detection_accuracy']:.1%}")
            
        except Exception as e:
            self.test_results["language_registry"] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ Failed: {e}")
    
    async def test_processing_pipeline(self):
        """Test processing pipeline functionality"""
        print("\n⚙️ Testing Processing Pipeline...")
        
        try:
            from processing_pipeline import ProcessingStage, StageStatus
            
            # Create test stages
            class TestStage1(ProcessingStage):
                def get_stage_name(self): return "test_stage_1"
                def get_dependencies(self): return []
                async def process(self, input_data, context):
                    return {"stage1_output": "test_data"}
            
            class TestStage2(ProcessingStage):
                def get_stage_name(self): return "test_stage_2"
                def get_dependencies(self): return ["test_stage_1"]
                async def process(self, input_data, context):
                    return {"stage2_output": input_data["test_stage_1"]["stage1_output"] + "_processed"}
            
            # Create pipeline
            pipeline = ProcessingPipeline("test_pipeline")
            pipeline.register_stage(TestStage1())
            pipeline.register_stage(TestStage2())
            
            # Validate pipeline
            validation = pipeline.validate_pipeline()
            
            # Execute pipeline
            input_data = {"test_input": "initial_data"}
            results = await pipeline.execute_pipeline(input_data)
            
            # Check results
            all_completed = all(result.status == StageStatus.COMPLETED for result in results.values())
            
            self.test_results["processing_pipeline"] = {
                "status": "PASSED" if all_completed else "FAILED",
                "pipeline_valid": validation["valid"],
                "stages_completed": sum(1 for r in results.values() if r.status == StageStatus.COMPLETED),
                "total_stages": len(results),
                "execution_order": pipeline.execution_order,
                "parallel_groups": pipeline.parallel_groups
            }
            
            print(f"  ✅ Pipeline Valid: {validation['valid']}")
            print(f"  ✅ Stages Completed: {self.test_results['processing_pipeline']['stages_completed']}/{len(results)}")
            print(f"  ✅ Execution Order: {pipeline.execution_order}")
            
        except Exception as e:
            self.test_results["processing_pipeline"] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ Failed: {e}")
    
    async def test_chunk_system(self):
        """Test chunk system functionality"""
        print("\n🧩 Testing Chunk System...")
        
        try:
            # Create chunk registry
            registry = create_default_chunk_registry()
            
            # Mock LLM client
            class MockLLMClient:
                async def generate(self, prompt):
                    return f"Mock response for: {prompt[:50]}..."
            
            llm_client = MockLLMClient()
            
            # Test chunk generation
            test_contexts = [
                {
                    "chunk_type": "code_implementation",
                    "context": {
                        "code_content": "def hello():\n    print('Hello, World!')",
                        "file_path": "test.py",
                        "language": "python"
                    }
                },
                {
                    "chunk_type": "architectural_pattern",
                    "context": {
                        "architectural_analysis": {"patterns": ["MVC", "Observer"]},
                        "source_files": ["test1.py", "test2.py"]
                    }
                }
            ]
            
            generated_chunks = []
            for test_case in test_contexts:
                try:
                    chunk = await registry.generate_chunk(
                        test_case["chunk_type"],
                        test_case["context"],
                        llm_client
                    )
                    generated_chunks.append(chunk)
                except Exception as e:
                    logger.error(f"Failed to generate {test_case['chunk_type']}: {e}")
            
            # Get statistics
            stats = registry.get_generation_statistics()
            
            self.test_results["chunk_system"] = {
                "status": "PASSED",
                "registered_types": len(registry.get_registered_types()),
                "chunks_generated": len(generated_chunks),
                "generation_stats": stats,
                "chunk_types": [chunk.metadata.chunk_type for chunk in generated_chunks]
            }
            
            print(f"  ✅ Registered Types: {len(registry.get_registered_types())}")
            print(f"  ✅ Chunks Generated: {len(generated_chunks)}")
            print(f"  ✅ Types: {registry.get_registered_types()}")
            
        except Exception as e:
            self.test_results["chunk_system"] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ Failed: {e}")
    
    async def test_integrated_system(self):
        """Test integrated system functionality"""
        print("\n🔗 Testing Integrated System...")
        
        try:
            # Create integrated system
            self.system = IntegratedCodeAnalysisSystem()
            
            # Get system info
            info = self.system.get_system_info()
            
            # Validate system
            validation = self.system.validate_system()
            
            self.test_results["integrated_system"] = {
                "status": "PASSED" if validation["system_valid"] else "FAILED",
                "system_valid": validation["system_valid"],
                "supported_languages": len(info["framework_info"]["supported_languages"]),
                "supported_extensions": len(info["framework_info"]["supported_extensions"]),
                "pipeline_stages": info["pipeline_info"]["total_stages"],
                "chunk_types": len(info["chunk_registry_info"]["registered_types"])
            }
            
            print(f"  ✅ System Valid: {validation['system_valid']}")
            print(f"  ✅ Languages: {len(info['framework_info']['supported_languages'])}")
            print(f"  ✅ Extensions: {len(info['framework_info']['supported_extensions'])}")
            print(f"  ✅ Pipeline Stages: {info['pipeline_info']['total_stages']}")
            
        except Exception as e:
            self.test_results["integrated_system"] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ Failed: {e}")
    
    async def test_file_processing(self):
        """Test file processing with sample files"""
        print("\n📁 Testing File Processing...")
        
        try:
            if not self.system:
                self.system = IntegratedCodeAnalysisSystem()
            
            # Create temporary test files
            with tempfile.TemporaryDirectory() as temp_dir:
                test_files = {
                    "test.py": "def hello():\n    print('Hello, World!')\n\nclass TestClass:\n    pass",
                    "test.cpp": "#include <iostream>\nint main() {\n    std::cout << \"Hello\" << std::endl;\n    return 0;\n}",
                    "test.h": "#ifndef TEST_H\n#define TEST_H\nvoid test_function();\n#endif",
                    "test.js": "function hello() {\n    console.log('Hello');\n}\n\nmodule.exports = { hello };",
                    "README.md": "# Test Project\nThis is a test project."
                }
                
                # Write test files
                for filename, content in test_files.items():
                    file_path = Path(temp_dir) / filename
                    file_path.write_text(content)
                
                # Analyze codebase
                try:
                    result = await self.system.analyze_codebase(temp_dir)
                    
                    self.test_results["file_processing"] = {
                        "status": "PASSED" if result["success"] else "FAILED",
                        "files_processed": result["total_files"],
                        "pipeline_success": result["success"],
                        "processing_time": result["processing_summary"]["total_processing_time"],
                        "stages_completed": result["processing_summary"]["stages_completed"]
                    }
                    
                    print(f"  ✅ Files Processed: {result['total_files']}")
                    print(f"  ✅ Pipeline Success: {result['success']}")
                    print(f"  ✅ Processing Time: {result['processing_summary']['total_processing_time']:.2f}s")
                    
                except Exception as e:
                    self.test_results["file_processing"] = {
                        "status": "FAILED",
                        "error": f"Analysis failed: {str(e)}"
                    }
                    print(f"  ❌ Analysis Failed: {e}")
            
        except Exception as e:
            self.test_results["file_processing"] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ Failed: {e}")
    
    async def test_query_processing(self):
        """Test query processing functionality"""
        print("\n🔍 Testing Query Processing...")
        
        try:
            if not self.system:
                self.system = IntegratedCodeAnalysisSystem()
            
            test_queries = [
                "How does the authentication system work?",
                "Find all Python functions",
                "What design patterns are used?",
                "Show me C++ memory management",
                "What is this codebase for?"
            ]
            
            query_results = []
            for query in test_queries:
                result = await self.system.process_query(query)
                query_results.append({
                    "query": query,
                    "strategy": result["processing_strategy"],
                    "recommended_processors": len(result["recommended_processors"]),
                    "system_ready": result["system_ready"]
                })
            
            self.test_results["query_processing"] = {
                "status": "PASSED",
                "queries_processed": len(query_results),
                "all_system_ready": all(r["system_ready"] for r in query_results),
                "strategies_used": list(set(r["strategy"] for r in query_results)),
                "query_results": query_results
            }
            
            print(f"  ✅ Queries Processed: {len(query_results)}")
            print(f"  ✅ System Ready: {all(r['system_ready'] for r in query_results)}")
            print(f"  ✅ Strategies: {self.test_results['query_processing']['strategies_used']}")
            
        except Exception as e:
            self.test_results["query_processing"] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ Failed: {e}")
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        passed_tests = sum(1 for result in self.test_results.values() if result.get("status") == "PASSED")
        total_tests = len(self.test_results)
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
                "overall_status": "PASSED" if passed_tests == total_tests else "FAILED"
            },
            "detailed_results": self.test_results,
            "recommendations": self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        for test_name, result in self.test_results.items():
            if result.get("status") == "FAILED":
                recommendations.append(f"Fix issues in {test_name}: {result.get('error', 'Unknown error')}")
        
        if not recommendations:
            recommendations.append("All tests passed! Framework is ready for production use.")
        
        return recommendations

async def main():
    """Main test execution"""
    print("🚀 Language-Agnostic Framework Test Suite")
    print("=" * 50)
    
    tester = FrameworkTester()
    report = await tester.run_all_tests()
    
    print("\n" + "=" * 50)
    print("📊 FINAL TEST REPORT")
    print("=" * 50)
    
    summary = report["summary"]
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['passed_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Success Rate: {summary['success_rate']:.1%}")
    print(f"Overall Status: {summary['overall_status']}")
    
    print("\n📋 Recommendations:")
    for rec in report["recommendations"]:
        print(f"  • {rec}")
    
    print("\n✨ Framework testing complete!")
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
