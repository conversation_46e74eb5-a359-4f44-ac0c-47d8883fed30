"""
Comprehensive Semantic Patterns for Language Processing
Migrated from the old code_preprocessor.py implementation
"""

from typing import Dict, List, Any
import re

class SemanticPatternRegistry:
    """Registry for semantic patterns across all supported languages"""
    
    def __init__(self):
        self.patterns = self._init_comprehensive_patterns()
    
    def _init_comprehensive_patterns(self) -> Dict[str, Dict[str, List[str]]]:
        """Initialize comprehensive semantic patterns from old implementation"""
        
        return {
            'function_patterns': {
                'c': [
                    r'(?:^|\n)\s*(?:static\s+)?(?:inline\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*{',  # Function definitions
                    r'(?:^|\n)\s*(?:extern\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*;',  # Function declarations
                    r'(?:^|\n)\s*#define\s+(\w+)\s*\(',  # Macro functions
                    r'(?:^|\n)\s*typedef\s+\w+\s*\(\s*\*\s*(\w+)\s*\)',  # Function pointers
                ],
                'cpp': [
                    r'(?:^|\n)\s*(?:static\s+)?(?:inline\s+)?(?:virtual\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*(?:const\s*)?{',  # Method definitions
                    r'(?:^|\n)\s*(?:static\s+)?(?:inline\s+)?(?:virtual\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*(?:const\s*)?;',  # Method declarations
                    r'(?:^|\n)\s*(\w+)\s*::\s*(\w+)\s*\([^)]*\)\s*(?:const\s*)?{',  # Class method implementations
                    r'(?:^|\n)\s*template\s*<[^>]*>\s*(?:\w+\s+)+(\w+)\s*\([^)]*\)',  # Template functions
                    r'(?:^|\n)\s*operator\s*([+\-*/=<>!&|^%]+)\s*\([^)]*\)',  # Operator overloads
                ],
                'python': [
                    r'(?:^|\n)\s*def\s+(\w+)\s*\(',  # Regular functions
                    r'(?:^|\n)\s*async\s+def\s+(\w+)\s*\(',  # Async functions
                    r'(?:^|\n)\s*@\w+\s*\n\s*def\s+(\w+)\s*\(',  # Decorated functions
                    r'(?:^|\n)\s*(\w+)\s*=\s*lambda\s*[^:]*:',  # Lambda functions
                ],
                'csharp': [
                    r'(?:^|\n)\s*(?:public|private|protected|internal)?\s*(?:static\s+)?(?:virtual\s+)?(?:override\s+)?(?:async\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*{',  # Method definitions
                    r'(?:^|\n)\s*(?:public|private|protected|internal)?\s*(?:static\s+)?(?:virtual\s+)?(?:abstract\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*;',  # Method declarations
                    r'(?:^|\n)\s*(\w+)\s*=>\s*',  # Expression-bodied members
                ],
                'javascript': [
                    r'(?:^|\n)\s*function\s+(\w+)\s*\(',  # Function declarations
                    r'(?:^|\n)\s*(?:const|let|var)\s+(\w+)\s*=\s*function\s*\(',  # Function expressions
                    r'(?:^|\n)\s*(?:const|let|var)\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*',  # Arrow functions
                    r'(?:^|\n)\s*(\w+)\s*:\s*function\s*\(',  # Object method shorthand
                    r'(?:^|\n)\s*(\w+)\s*\([^)]*\)\s*{',  # Method definitions in classes
                    r'(?:^|\n)\s*async\s+(\w+)\s*\(',  # Async functions
                ],
                'typescript': [
                    r'(?:^|\n)\s*function\s+(\w+)\s*\(',  # Function declarations
                    r'(?:^|\n)\s*(?:const|let|var)\s+(\w+)\s*=\s*\([^)]*\)\s*:\s*\w+\s*=>\s*',  # Typed arrow functions
                    r'(?:^|\n)\s*(\w+)\s*\([^)]*\)\s*:\s*\w+\s*{',  # Typed method definitions
                    r'(?:^|\n)\s*async\s+(\w+)\s*\([^)]*\)\s*:\s*Promise<\w+>',  # Async typed functions
                ],
                'java': [
                    r'(?:^|\n)\s*(?:public|private|protected)?\s*(?:static\s+)?(?:final\s+)?(?:synchronized\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*(?:throws\s+\w+\s*)?{',  # Method definitions
                    r'(?:^|\n)\s*(?:public|private|protected)?\s*(?:static\s+)?(?:abstract\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*(?:throws\s+\w+\s*)?;',  # Method declarations
                ],
                'go': [
                    r'(?:^|\n)\s*func\s+(\w+)\s*\([^)]*\)',  # Function definitions
                    r'(?:^|\n)\s*func\s+\(\s*\w+\s+\*?\w+\s*\)\s+(\w+)\s*\([^)]*\)',  # Method definitions
                ],
                'rust': [
                    r'(?:^|\n)\s*(?:pub\s+)?fn\s+(\w+)\s*\([^)]*\)',  # Function definitions
                    r'(?:^|\n)\s*(?:pub\s+)?async\s+fn\s+(\w+)\s*\([^)]*\)',  # Async function definitions
                ],
                'metta': [
                    r'(?:^|\n)\s*\(\s*=\s+(\w+)',  # Function definitions: (= function_name ...)
                    r'(?:^|\n)\s*\(\s*:\s+(\w+)',  # Type definitions: (: function_name ...)
                    r'(?:^|\n)\s*\(\s*lambda\s+\(\s*(\w+)',  # Lambda functions: (lambda (param) ...)
                    r'(?:^|\n)\s*\(\s*define\s+(\w+)',  # Define statements: (define name ...)
                ],
                'prolog': [
                    r'(?:^|\n)\s*(\w+)\s*\([^)]*\)\s*:-',  # Rule definitions: predicate(args) :-
                    r'(?:^|\n)\s*(\w+)\s*\([^)]*\)\s*\.',  # Fact definitions: predicate(args).
                    r'(?:^|\n)\s*(\w+)\s*:-',  # Simple rules: predicate :-
                    r'(?:^|\n)\s*(\w+)\s*\.',  # Simple facts: predicate.
                ],
                'sql': [
                    r'(?:^|\n)\s*CREATE\s+(?:OR\s+REPLACE\s+)?(?:FUNCTION|PROCEDURE)\s+(\w+)\s*\(',  # Function/procedure definitions
                    r'(?:^|\n)\s*DELIMITER\s+\$\$\s*CREATE\s+FUNCTION\s+(\w+)\s*\(',  # MySQL functions
                ],
            },
            
            'class_patterns': {
                'c': [
                    r'(?:^|\n)\s*struct\s+(\w+)\s*{',  # Struct definitions
                    r'(?:^|\n)\s*typedef\s+struct\s+(\w+)\s*{',  # Typedef struct definitions
                    r'(?:^|\n)\s*union\s+(\w+)\s*{',  # Union definitions
                    r'(?:^|\n)\s*enum\s+(\w+)\s*{',  # Enum definitions
                ],
                'cpp': [
                    r'(?:^|\n)\s*class\s+(\w+)\s*(?::\s*(?:public|private|protected)\s+\w+\s*)?{',  # Class definitions
                    r'(?:^|\n)\s*struct\s+(\w+)\s*(?::\s*(?:public|private|protected)\s+\w+\s*)?{',  # Struct definitions
                    r'(?:^|\n)\s*template\s*<[^>]*>\s*class\s+(\w+)',  # Template class definitions
                    r'(?:^|\n)\s*namespace\s+(\w+)\s*{',  # Namespace definitions
                ],
                'python': [
                    r'(?:^|\n)\s*class\s+(\w+)\s*(?:\([^)]*\))?\s*:',  # Class definitions
                    r'(?:^|\n)\s*@dataclass\s*\n\s*class\s+(\w+)',  # Dataclass definitions
                ],
                'csharp': [
                    r'(?:^|\n)\s*(?:public|private|protected|internal)?\s*(?:static\s+)?(?:abstract\s+)?(?:sealed\s+)?class\s+(\w+)\s*(?::\s*\w+\s*)?{',  # Class definitions
                    r'(?:^|\n)\s*(?:public|private|protected|internal)?\s*interface\s+(\w+)\s*(?::\s*\w+\s*)?{',  # Interface definitions
                    r'(?:^|\n)\s*(?:public|private|protected|internal)?\s*struct\s+(\w+)\s*(?::\s*\w+\s*)?{',  # Struct definitions
                    r'(?:^|\n)\s*(?:public|private|protected|internal)?\s*enum\s+(\w+)\s*{',  # Enum definitions
                ],
                'javascript': [
                    r'(?:^|\n)\s*class\s+(\w+)\s*(?:extends\s+\w+\s*)?{',  # Class definitions
                    r'(?:^|\n)\s*(?:const|let|var)\s+(\w+)\s*=\s*class\s*(?:extends\s+\w+\s*)?{',  # Class expressions
                ],
                'typescript': [
                    r'(?:^|\n)\s*(?:export\s+)?(?:abstract\s+)?class\s+(\w+)\s*(?:extends\s+\w+\s*)?(?:implements\s+\w+\s*)?{',  # Class definitions
                    r'(?:^|\n)\s*(?:export\s+)?interface\s+(\w+)\s*(?:extends\s+\w+\s*)?{',  # Interface definitions
                    r'(?:^|\n)\s*(?:export\s+)?type\s+(\w+)\s*=',  # Type definitions
                ],
                'java': [
                    r'(?:^|\n)\s*(?:public|private|protected)?\s*(?:static\s+)?(?:final\s+)?(?:abstract\s+)?class\s+(\w+)\s*(?:extends\s+\w+\s*)?(?:implements\s+\w+\s*)?{',  # Class definitions
                    r'(?:^|\n)\s*(?:public|private|protected)?\s*interface\s+(\w+)\s*(?:extends\s+\w+\s*)?{',  # Interface definitions
                    r'(?:^|\n)\s*(?:public|private|protected)?\s*enum\s+(\w+)\s*{',  # Enum definitions
                ],
                'go': [
                    r'(?:^|\n)\s*type\s+(\w+)\s+struct\s*{',  # Struct definitions
                    r'(?:^|\n)\s*type\s+(\w+)\s+interface\s*{',  # Interface definitions
                ],
                'rust': [
                    r'(?:^|\n)\s*(?:pub\s+)?struct\s+(\w+)\s*(?:<[^>]*>)?\s*{',  # Struct definitions
                    r'(?:^|\n)\s*(?:pub\s+)?enum\s+(\w+)\s*(?:<[^>]*>)?\s*{',  # Enum definitions
                    r'(?:^|\n)\s*(?:pub\s+)?trait\s+(\w+)\s*(?:<[^>]*>)?\s*{',  # Trait definitions
                ],
                'metta': [
                    r'(?:^|\n)\s*\(\s*:\s+(\w+)\s+Type\)',  # Type definitions: (: TypeName Type)
                    r'(?:^|\n)\s*\(\s*:\s+(\w+)\s+Atom\)',  # Atom type definitions
                    r'(?:^|\n)\s*\(\s*:\s+(\w+)\s+\->\s*',  # Function type signatures
                    r'(?:^|\n)\s*\(\s*match\s+&(\w+)',  # Pattern matching on types
                ],
                'prolog': [
                    r'(?:^|\n)\s*:-\s*module\s*\(\s*(\w+)',  # Module definitions: :- module(name, [...]).
                    r'(?:^|\n)\s*:-\s*use_module\s*\(\s*(\w+)',  # Module imports: :- use_module(name).
                    r'(?:^|\n)\s*:-\s*dynamic\s+(\w+)',  # Dynamic predicate declarations
                    r'(?:^|\n)\s*:-\s*multifile\s+(\w+)',  # Multifile predicate declarations
                ],
            },
            
            'import_patterns': {
                'c': [
                    r'(?:^|\n)\s*#include\s*[<"](.*?)[>"]',  # Include statements
                ],
                'cpp': [
                    r'(?:^|\n)\s*#include\s*[<"](.*?)[>"]',  # Include statements
                    r'(?:^|\n)\s*using\s+namespace\s+(\w+)',  # Using namespace
                    r'(?:^|\n)\s*using\s+(\w+::\w+)',  # Using declarations
                ],
                'python': [
                    r'(?:^|\n)\s*import\s+([\w.]+)',  # Import statements
                    r'(?:^|\n)\s*from\s+([\w.]+)\s+import',  # From import statements
                    r'(?:^|\n)\s*import\s+([\w.]+)\s+as\s+\w+',  # Import as statements
                ],
                'csharp': [
                    r'(?:^|\n)\s*using\s+([\w.]+)\s*;',  # Using statements
                    r'(?:^|\n)\s*using\s+(\w+)\s*=\s*[\w.]+\s*;',  # Using alias statements
                ],
                'javascript': [
                    r'(?:^|\n)\s*import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',  # ES6 import statements
                    r'(?:^|\n)\s*import\s+[\'"]([^\'"]+)[\'"]',  # ES6 import statements (side effects)
                    r'(?:^|\n)\s*(?:const|let|var)\s+.*?\s*=\s*require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',  # CommonJS require
                ],
                'typescript': [
                    r'(?:^|\n)\s*import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',  # ES6 import statements
                    r'(?:^|\n)\s*import\s+[\'"]([^\'"]+)[\'"]',  # ES6 import statements (side effects)
                    r'(?:^|\n)\s*import\s+type\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',  # TypeScript type imports
                ],
                'java': [
                    r'(?:^|\n)\s*import\s+([\w.]+)\s*;',  # Import statements
                    r'(?:^|\n)\s*import\s+static\s+([\w.]+)\s*;',  # Static import statements
                ],
                'go': [
                    r'(?:^|\n)\s*import\s+[\'"]([^\'"]+)[\'"]',  # Import statements
                    r'(?:^|\n)\s*import\s+\w+\s+[\'"]([^\'"]+)[\'"]',  # Aliased import statements
                ],
                'rust': [
                    r'(?:^|\n)\s*use\s+([\w:]+)',  # Use statements
                    r'(?:^|\n)\s*extern\s+crate\s+(\w+)',  # External crate statements
                ],
                'metta': [
                    r'(?:^|\n)\s*\(\s*import!\s+(\w+)',  # Import statements: (import! module)
                    r'(?:^|\n)\s*\(\s*include\s+(\w+)',  # Include statements: (include file)
                    r'(?:^|\n)\s*\(\s*load\s+(\w+)',  # Load statements: (load file)
                    r'(?:^|\n)\s*\(\s*use\s+(\w+)',  # Use statements: (use module)
                ],
                'prolog': [
                    r'(?:^|\n)\s*:-\s*use_module\s*\(\s*library\s*\(\s*(\w+)',  # Library imports
                    r'(?:^|\n)\s*:-\s*use_module\s*\(\s*(\w+)',  # Module imports
                    r'(?:^|\n)\s*:-\s*include\s*\(\s*(\w+)',  # File includes
                    r'(?:^|\n)\s*:-\s*consult\s*\(\s*(\w+)',  # File consultations
                ],
            },
            
            'variable_patterns': {
                'c': [
                    r'(?:^|\n)\s*(?:static\s+)?(?:const\s+)?(?:extern\s+)?(\w+)\s+(\w+)\s*(?:=|;)',  # Variable declarations
                    r'(?:^|\n)\s*#define\s+(\w+)\s+([^\n]+)',  # Macro definitions
                ],
                'cpp': [
                    r'(?:^|\n)\s*(?:static\s+)?(?:const\s+)?(?:extern\s+)?(\w+)\s+(\w+)\s*(?:=|;)',  # Variable declarations
                    r'(?:^|\n)\s*auto\s+(\w+)\s*=',  # Auto variable declarations
                    r'(?:^|\n)\s*constexpr\s+(?:\w+\s+)?(\w+)\s*=',  # Constexpr declarations
                ],
                'python': [
                    r'(?:^|\n)\s*(\w+)\s*=\s*[^=]',  # Variable assignments
                    r'(?:^|\n)\s*(\w+)\s*:\s*\w+\s*=',  # Type annotated assignments
                    r'(?:^|\n)\s*global\s+(\w+)',  # Global declarations
                ],
                'csharp': [
                    r'(?:^|\n)\s*(?:public|private|protected|internal)?\s*(?:static\s+)?(?:readonly\s+)?(?:const\s+)?(\w+)\s+(\w+)\s*(?:=|;)',  # Field/variable declarations
                    r'(?:^|\n)\s*var\s+(\w+)\s*=',  # Var declarations
                ],
                'javascript': [
                    r'(?:^|\n)\s*(?:const|let|var)\s+(\w+)\s*=',  # Variable declarations
                    r'(?:^|\n)\s*(\w+)\s*=\s*[^=]',  # Variable assignments (global scope)
                ],
                'typescript': [
                    r'(?:^|\n)\s*(?:const|let|var)\s+(\w+)\s*:\s*\w+\s*=',  # Typed variable declarations
                    r'(?:^|\n)\s*(?:const|let|var)\s+(\w+)\s*=',  # Variable declarations
                ],
                'java': [
                    r'(?:^|\n)\s*(?:public|private|protected)?\s*(?:static\s+)?(?:final\s+)?(\w+)\s+(\w+)\s*(?:=|;)',  # Field/variable declarations
                ],
                'go': [
                    r'(?:^|\n)\s*var\s+(\w+)\s+\w+',  # Variable declarations
                    r'(?:^|\n)\s*(\w+)\s*:=',  # Short variable declarations
                ],
                'rust': [
                    r'(?:^|\n)\s*(?:pub\s+)?(?:static\s+)?(?:const\s+)?let\s+(?:mut\s+)?(\w+)',  # Variable declarations
                    r'(?:^|\n)\s*(?:pub\s+)?const\s+(\w+)',  # Constant declarations
                ],
                'metta': [
                    r'(?:^|\n)\s*\(\s*=\s+(\w+)\s+',  # Variable assignments: (= var value)
                    r'(?:^|\n)\s*\(\s*let\s+(\w+)',  # Let bindings: (let var ...)
                    r'(?:^|\n)\s*\(\s*let\*\s+\(\s*(\w+)',  # Let* bindings: (let* ((var ...)))
                    r'(?:^|\n)\s*\(\s*define\s+(\w+)',  # Define statements: (define var ...)
                ],
                'prolog': [
                    r'(?:^|\n)\s*(\w+)\s*=\s*',  # Variable unification: Var = Value
                    r'(?:^|\n)\s*(\w+)\s*is\s*',  # Arithmetic evaluation: Var is Expression
                    r'(?:^|\n)\s*assert\s*\(\s*(\w+)',  # Dynamic assertions: assert(fact)
                    r'(?:^|\n)\s*retract\s*\(\s*(\w+)',  # Dynamic retractions: retract(fact)
                ],
            }
        }
    
    def get_patterns_for_language(self, language: str, pattern_type: str) -> List[str]:
        """Get patterns for a specific language and pattern type"""
        return self.patterns.get(pattern_type, {}).get(language, [])
    
    def get_all_patterns_for_language(self, language: str) -> Dict[str, List[str]]:
        """Get all patterns for a specific language"""
        result = {}
        for pattern_type, lang_patterns in self.patterns.items():
            if language in lang_patterns:
                result[pattern_type] = lang_patterns[language]
        return result
    
    def extract_semantic_elements(self, content: str, language: str) -> Dict[str, List[str]]:
        """Extract all semantic elements from content using language-specific patterns"""
        results = {}
        
        for pattern_type, patterns in self.get_all_patterns_for_language(language).items():
            elements = []
            for pattern in patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.IGNORECASE)
                if isinstance(matches[0], tuple) if matches else False:
                    # If pattern returns tuples, flatten them
                    elements.extend([match[0] if isinstance(match, tuple) else match for match in matches])
                else:
                    elements.extend(matches)
            
            results[pattern_type] = list(set(elements))  # Remove duplicates
        
        return results

# Global instance
semantic_registry = SemanticPatternRegistry()
