# TODO: RAG System Software Enhancements

## 🚀 High Priority Software Improvements

### 1. LLM-Generated Chunk Summaries
**Status**: 📋 Planned  
**Priority**: High  
**Estimated Impact**: +15-25% success rate improvement

#### Description
Process each code chunk with an LLM to generate 1-3 sentence summaries describing the chunk's purpose, functionality, and role in the larger system.

#### Implementation Options
- **Option 1 (Recommended)**: Dual Embedding - Store both original code AND summary
- **Option 2**: Enhanced Content - Prepend summary to original code content
- **Option 3**: Summary-Only - Replace code with summaries (not recommended)

#### Expected Benefits (Enhanced Context-Aware + Architectural Approach)

**Code-Level Improvements:**
- **Modular Understanding**: Chunks understood within their file's cohesive purpose
- **C/C++ Interface-Implementation Awareness**: Header-implementation relationships clearly understood
- **Language-Specific Context**: Tailored analysis for different programming paradigms
- **Enhanced Semantic Matching**: Superior "how does X work?" and "what does Y do?" responses
- **Cross-Reference Quality**: Excellent relationships between related functions/classes

**Architectural-Level Improvements:**
- **System Architecture Queries**: Excellent responses to "What are the main components?"
- **Design Pattern Recognition**: Clear identification of architectural patterns used
- **Module Relationship Mapping**: Understanding of how modules interact and depend on each other
- **High-Level Conceptual Search**: Searchable architectural and design concept chunks
- **Interface Documentation**: Clear understanding of APIs and public interfaces

**Performance Metrics:**
- **Similarity Scores**: Expected improvement from ~0.254 to 0.6-0.8 (enhanced context + architectural chunks)
- **Context Utilization**: Expected improvement from ~60% to 85-95%
- **Query Success Rate**: +25-35% improvement for code queries, +40-60% for architectural queries
- **Chunk Diversity**: 20-30% more chunks (including architectural chunks) for better coverage

**Query Type Improvements:**
- **Implementation Queries**: "find tmwmem_alloc function" → Excellent (header-impl context)
- **Architectural Queries**: "What are the main modules?" → Dramatically improved
- **Design Pattern Queries**: "show me error handling patterns" → Much better recognition
- **Interface Queries**: "What APIs does this module provide?" → New capability
- **System Understanding**: "How does the memory management work?" → Comprehensive responses

#### Implementation Details - Context-Aware Summarization

**🎯 Key Innovation: Full File Context Analysis**
Instead of analyzing chunks in isolation, provide the entire file context to understand the chunk's role within its module.

```python
# Language-aware enhanced context prompt
def build_enhanced_context_prompt(chunk, file_path, file_context, context_content):
    if file_context["analysis_scope"] == "header_implementation_pair":
        # C/C++ header-implementation pair analysis
        return f"""
You are analyzing a code chunk within a C/C++ header-implementation file pair. This leverages the modular nature of C/C++ where headers declare interfaces and implementations provide functionality.

HEADER-IMPLEMENTATION CONTEXT:
Header File: {context_content.get('header', {}).get('path', 'N/A')}
Header Purpose: {context_content.get('header', {}).get('purpose', 'N/A')}
Header Content:
{context_content.get('header', {}).get('content', 'N/A')[:1500]}

Implementation File: {context_content.get('implementation', {}).get('path', 'N/A')}
Implementation Purpose: {context_content.get('implementation', {}).get('purpose', 'N/A')}
Implementation Content:
{context_content.get('implementation', {}).get('content', 'N/A')[:1500]}

TARGET CHUNK TO SUMMARIZE:
File: {file_path}
Lines {chunk.start_line}-{chunk.end_line}:
{chunk.content}

TASK: Provide a 1-3 sentence summary considering:
1. Interface vs implementation relationship (if applicable)
2. How this chunk relates to declarations/definitions in paired file
3. Architectural role within the module (data structures, algorithms, APIs)
4. Dependencies and relationships across header-implementation boundary

Summary:
"""
    else:
        # Generic file context analysis
        return f"""
You are analyzing a code chunk within its full file context, leveraging the modular nature of software.

FILE CONTEXT:
File: {file_path}
Language: {file_context.get('language', 'unknown')}
File Purpose: {context_content.get('purpose', 'N/A')}
Full File Content:
{context_content.get('content', 'N/A')[:2000]}

TARGET CHUNK TO SUMMARIZE:
Lines {chunk.start_line}-{chunk.end_line}:
{chunk.content}

TASK: Provide a 1-3 sentence summary describing:
1. What this chunk does (primary purpose)
2. How it relates to the file's overall functionality
3. Key algorithms, patterns, or architectural role
4. Dependencies or relationships to other parts of the file

Summary:
"""
```

#### Benefits of Context-Aware Summarization

**🔍 Enhanced Understanding:**
- **Modular Context**: Understand how chunk fits within file's purpose
- **Dependency Awareness**: Identify relationships to other functions/classes
- **Architectural Role**: Better understanding of design patterns and structure
- **Cross-Reference Quality**: Improved relationships between related chunks

**📊 Expected Improvements:**
- **Similarity Scores**: 0.254 → 0.5-0.7 (vs 0.4-0.6 without context)
- **Semantic Accuracy**: Much better understanding of chunk purpose
- **Query Matching**: Excellent for "how does X work?" and architectural queries
- **Cross-File Understanding**: Better relationships between modules

#### Advanced Implementation Strategy

```python
class LanguageAwareContextSummarizer:
    def __init__(self):
        self.file_cache = {}
        self.header_impl_pairs = {}  # C/C++ header-implementation mapping
        self.architectural_chunks = []  # Generated high-level chunks

    async def analyze_file_relationships(self, file_path):
        """Determine language-specific file relationships"""
        language = self.detect_language(file_path)

        if language in ['c', 'cpp', 'cxx']:
            return await self.analyze_c_cpp_relationships(file_path)
        elif language in ['python']:
            return await self.analyze_python_module_context(file_path)
        elif language in ['java', 'csharp']:
            return await self.analyze_class_based_context(file_path)
        else:
            return await self.analyze_generic_context(file_path)

    async def analyze_c_cpp_relationships(self, file_path):
        """Handle C/C++ header-implementation pairs"""
        if file_path.endswith(('.h', '.hpp', '.hxx')):
            # Header file - find corresponding implementation
            impl_file = self.find_implementation_file(file_path)
            if impl_file:
                return {
                    "type": "header",
                    "paired_file": impl_file,
                    "context_files": [file_path, impl_file],
                    "analysis_scope": "header_implementation_pair"
                }
        elif file_path.endswith(('.c', '.cpp', '.cxx')):
            # Implementation file - find corresponding header
            header_file = self.find_header_file(file_path)
            if header_file:
                return {
                    "type": "implementation",
                    "paired_file": header_file,
                    "context_files": [header_file, file_path],
                    "analysis_scope": "header_implementation_pair"
                }

        return {"type": "standalone", "context_files": [file_path]}

    async def summarize_chunk_with_enhanced_context(self, chunk, file_path):
        # Analyze file relationships
        file_context = await self.analyze_file_relationships(file_path)

        # Build comprehensive context
        context_content = await self.build_comprehensive_context(file_context)

        # Enhanced prompt with language-aware context
        prompt = self.build_enhanced_context_prompt(
            chunk=chunk,
            file_path=file_path,
            file_context=file_context,
            context_content=context_content
        )

        # Generate context-aware summary
        summary = await self.llm_client.generate(prompt)

        # Generate architectural chunks as side effect
        architectural_chunk = await self.generate_architectural_chunk(
            chunk, file_context, summary
        )

        if architectural_chunk:
            self.architectural_chunks.append(architectural_chunk)

        return {
            "summary": summary,
            "file_context": file_context,
            "chunk_position": chunk.line_range,
            "architectural_insights": architectural_chunk
        }

    async def build_comprehensive_context(self, file_context):
        """Build context considering language-specific relationships"""
        context_files = file_context["context_files"]
        combined_content = {}

        for file_path in context_files:
            content = await self.get_file_content(file_path)
            file_type = "header" if file_path.endswith(('.h', '.hpp')) else "implementation"
            combined_content[file_type] = {
                "path": file_path,
                "content": content,
                "purpose": await self.analyze_file_purpose(content, file_path)
            }

        return combined_content

    async def generate_architectural_chunk(self, chunk, file_context, summary):
        """Generate high-level architectural/conceptual chunks"""
        if not self.should_generate_architectural_chunk(chunk, summary):
            return None

        architectural_prompt = f"""
        Based on this code analysis, generate a high-level architectural description:

        File Context: {file_context}
        Chunk Summary: {summary}
        Code Chunk: {chunk.content[:500]}...

        Generate a conceptual description focusing on:
        1. Architectural patterns or design principles demonstrated
        2. Module responsibilities and interfaces
        3. System-level functionality and purpose
        4. Relationships to other system components

        This should be searchable for queries like:
        - "What are the main architectural components?"
        - "How is the system organized?"
        - "What design patterns are used?"

        Architectural Description:
        """

        arch_description = await self.llm_client.generate(architectural_prompt)

        return {
            "type": "architectural",
            "content": arch_description,
            "source_files": file_context["context_files"],
            "related_chunks": [chunk.id],
            "keywords": self.extract_architectural_keywords(arch_description)
        }
```

#### Advanced Processing Optimization

**🚀 Language-Aware Batch Processing:**
```python
async def batch_process_with_enhanced_context():
    # Group chunks by language-specific relationships
    processing_groups = await group_chunks_by_relationships(all_chunks)

    architectural_chunks = []

    for group in processing_groups:
        if group["type"] == "header_implementation_pair":
            # C/C++ header-implementation processing
            summaries = await process_header_impl_pair(group)

        elif group["type"] == "module_group":
            # Python/Java module processing
            summaries = await process_module_group(group)

        else:
            # Single file processing
            summaries = await process_single_file(group)

        # Extract architectural chunks generated during processing
        for summary in summaries:
            if summary.get("architectural_insights"):
                architectural_chunks.append(summary["architectural_insights"])

    # Add architectural chunks to vector store
    await add_architectural_chunks_to_vector_store(architectural_chunks)

    return {
        "code_summaries": all_summaries,
        "architectural_chunks": architectural_chunks,
        "processing_time": "10-20 minutes with RTX 3090s"
    }

async def process_header_impl_pair(group):
    """Process C/C++ header-implementation pairs together"""
    header_file = group["header_file"]
    impl_file = group["impl_file"]

    # Load both files
    header_content = await load_file(header_file)
    impl_content = await load_file(impl_file)

    # Analyze interface-implementation relationship
    relationship_analysis = await analyze_header_impl_relationship(
        header_content, impl_content
    )

    # Process all chunks with full header-impl context
    all_chunks = group["header_chunks"] + group["impl_chunks"]

    tasks = []
    for chunk in all_chunks:
        task = summarize_chunk_with_enhanced_context(
            chunk,
            file_context={
                "type": "header_implementation_pair",
                "header": {"path": header_file, "content": header_content},
                "implementation": {"path": impl_file, "content": impl_content},
                "relationship": relationship_analysis
            }
        )
        tasks.append(task)

    return await asyncio.gather(*tasks)

async def generate_module_architectural_summary(group):
    """Generate high-level architectural chunks for modules"""
    module_summary_prompt = f"""
    Analyze this software module and generate architectural insights:

    Module Files: {[f["path"] for f in group["files"]]}
    Module Purpose: {group["module_purpose"]}
    Key Components: {group["key_components"]}

    Generate architectural descriptions for:
    1. Module's role in the overall system
    2. Key design patterns and architectural decisions
    3. Public interfaces and APIs provided
    4. Dependencies and relationships to other modules
    5. Data flow and control flow patterns

    These descriptions should be searchable for queries like:
    - "What are the main system components?"
    - "How is the memory management implemented?"
    - "What design patterns are used?"

    Architectural Insights:
    """

    return await llm_client.generate(module_summary_prompt)
```

#### Ollama Model Recommendations
Based on available models, optimal choices for chunk summarization:

| Model | Speed | Quality | VRAM | Best For |
|-------|-------|---------|------|----------|
| **deepseek-coder:6.7b** | ⚡ Fast | 🎯 Excellent | 8-10 GB | **Primary choice** |
| **codellama:7b-instruct** | ⚡ Fast | 🎯 Very Good | 8-10 GB | Alternative |
| **smollm2:1.7b** | 🚀 Ultra Fast | ✅ Good | 2-3 GB | Testing/prototyping |
| **deepseek-coder:33b** | 🐌 Slower | 🎯 Excellent | 20-24 GB | High-quality option |

#### Processing Implementation
```python
async def process_chunks_with_summaries():
    # Use distributed RTX processing for speed
    cluster = DistributedOllamaCluster()
    
    # Process in parallel across available RTX cards
    semaphore = asyncio.Semaphore(4)  # Limit concurrent requests
    
    async def process_chunk(chunk):
        async with semaphore:
            return await cluster.summarize_chunk(chunk)
    
    # Process all chunks in parallel
    tasks = [process_chunk(chunk) for chunk in chunks]
    summaries = await asyncio.gather(*tasks)
    
    # Expected time with RTX 3090s: 10-20 minutes for 2,283 chunks
```

#### Success Metrics
- Measure improvement on existing test query suite
- Target categories: Memory Management, Timer Management, Error Handling
- Expected query success rate improvement: +15-25%

---

### 2. Standalone Comment Block Extraction
**Status**: 📋 Planned  
**Priority**: Medium  
**Estimated Impact**: +5-10% for architectural queries

#### Description
Extract standalone comment blocks, file headers, and API documentation as separate searchable chunks in addition to the current code-embedded approach.

#### Current State
- Comments are **counted and analyzed** but not extracted as separate chunks
- Comments are included within function/class chunks but not independently searchable
- File header comments are filtered out during header processing

#### Proposed Enhancement
Extract as separate chunks:
1. **File Header Comments** - Overall file/module documentation
2. **API Documentation Comments** - Function/class documentation blocks
3. **Large Comment Blocks** - Design explanations, algorithm descriptions
4. **Inline Documentation** - Significant explanatory comments

#### Implementation Approach
```python
def extract_comment_blocks(source_code, filepath, language):
    """Extract standalone comment blocks as separate chunks"""
    comment_chunks = []
    
    # Extract file header comments
    header_comments = extract_file_header_comments(source_code, language)
    
    # Extract function/class documentation
    api_docs = extract_api_documentation(source_code, language)
    
    # Extract large comment blocks
    block_comments = extract_large_comment_blocks(source_code, language)
    
    return comment_chunks
```

#### Target Query Types
- `"What are the main modules in this codebase?"`
- `"What is the overall architecture philosophy?"`
- `"Show me API documentation for memory management"`
- `"What are the design principles behind TMW library?"`

#### Implementation Priority
- **Implement AFTER** LLM summaries to avoid diluting current performance
- **A/B Test** impact on existing query suite before full deployment

---

### 3. Embedding Model Optimization
**Status**: 📋 Planned
**Priority**: Medium
**Estimated Impact**: +10-20% similarity score improvement

#### Current State
- **Model**: `nomic-embed-text` (31.5M parameters)
- **Performance**: ~0.254 average similarity scores
- **Strengths**: Large context window, code-optimized, stable
- **Status**: Working adequately, no immediate issues

#### Available Ollama Embedding Models Analysis

| Model | Size | Strengths | Expected Improvement | Recommendation |
|-------|------|-----------|---------------------|----------------|
| **nomic-embed-text** | 31.5M | Large context, code-optimized | Current baseline | ⭐⭐⭐⭐ **Keep current** |
| **mxbai-embed-large** | 335M | State-of-the-art performance | 0.254 → 0.4-0.6 | ⭐⭐⭐⭐⭐ **Primary upgrade** |
| **snowflake-arctic-embed2** | 568M | Multilingual, latest generation | 0.254 → 0.5-0.7 | ⭐⭐⭐⭐ **Premium option** |
| **bge-m3** | 567M | Multi-functional, versatile | 0.254 → 0.4-0.6 | ⭐⭐⭐ **Specialized** |
| **snowflake-arctic-embed** | 22M-753M | Multiple sizes available | Variable | ⭐⭐⭐ **Flexible** |

#### Implementation Strategy

**Phase 1: Baseline Maintenance (Current)**
```python
# Continue with current setup
embedding_model = "nomic-embed-text"
# Rationale: Stable, working, focus on LLM summaries first
```

**Phase 2: Upgrade Evaluation (After LLM Summaries)**
```python
# Test upgrade on subset of codebase
async def test_embedding_upgrade():
    # Current model baseline
    current_results = await test_with_model("nomic-embed-text", test_chunks)

    # Test upgraded model
    upgrade_results = await test_with_model("mxbai-embed-large", test_chunks)

    # Compare performance metrics
    improvement = compare_similarity_scores(current_results, upgrade_results)

    if improvement > 0.1:  # 10% improvement threshold
        return "proceed_with_upgrade"
    else:
        return "keep_current"
```

**Phase 3: Production Migration (If Beneficial)**
```python
# Full codebase re-embedding
async def migrate_embeddings():
    # Backup current embeddings
    await backup_current_embeddings()

    # Re-embed entire codebase with new model
    new_embeddings = await re_embed_codebase("mxbai-embed-large")

    # Update vector database
    await update_vector_db(new_embeddings)

    # Validate performance improvement
    await validate_upgrade_success()
```

#### Expected Benefits

**mxbai-embed-large Upgrade:**
- **Similarity Scores**: 0.254 → 0.4-0.6 (estimated 60-135% improvement)
- **Semantic Understanding**: Better "how does X work?" query matching
- **Cross-Reference Detection**: Improved relationships between code chunks
- **Query Success Rate**: +10-20% improvement on existing test suite

**Implementation Considerations:**
- **Re-embedding Time**: 2-4 hours for full codebase (2,283 chunks)
- **VRAM Requirements**: 3-4 GB (suitable for RTX 3070+)
- **Storage**: Minimal increase in vector database size
- **Compatibility**: Drop-in replacement, no API changes needed

#### Testing Protocol
```python
# A/B testing framework for embedding comparison
class EmbeddingComparison:
    def __init__(self):
        self.test_queries = load_test_queries()  # From test_openwebui_api.py

    async def compare_models(self, model_a, model_b):
        results_a = await self.run_test_suite(model_a)
        results_b = await self.run_test_suite(model_b)

        return {
            "similarity_improvement": results_b.avg_similarity - results_a.avg_similarity,
            "chunks_retrieved_change": results_b.avg_chunks - results_a.avg_chunks,
            "context_utilization_change": results_b.avg_utilization - results_a.avg_utilization,
            "query_success_rate_change": results_b.success_rate - results_a.success_rate
        }
```

#### Recommended Timeline
1. **Immediate**: Continue with `nomic-embed-text` (stable baseline)
2. **After LLM Summaries**: Test `mxbai-embed-large` on subset
3. **If Beneficial**: Full migration to upgraded embedding model
4. **Future**: Consider `snowflake-arctic-embed2` for advanced features

---

### 4. RAG Server API Enhancements
**Status**: ✅ Partially Complete  
**Priority**: Medium

#### Completed Fixes
- ✅ Fixed RAG Query HTTP 404 errors
- ✅ Updated endpoint from `/api/query` → `/tools/search_code`
- ✅ Fixed payload format: `codebase` → `codebase_name`, `max_chunks` → `n_results`
- ✅ Added response parsing for RAG server format
- ✅ Added type annotation for `quality_counts: dict[str, int]`

#### Remaining Enhancements
- **Parallel RAG Querying**: Enhance coordination between OpenWebUI and direct RAG server
- **Context Quality Metrics**: Improve analysis of RAG context utilization
- **Chunk Count Reporting**: Better handling of zero-chunk scenarios
- **Cross-Reference Enhancement**: Improve relationships between code chunks

---

### 4. Distributed Processing Coordination
**Status**: 📋 Planned  
**Priority**: High

#### Ollama v0.9.5 Network Exposure
- **Network Access**: Ollama can now be exposed across network devices
- **Cross-Platform**: Windows and Linux machines work seamlessly
- **Simplified Coordination**: Direct HTTP API calls, no SSH/PowerShell remoting needed

#### Implementation Architecture
```python
class DistributedOllamaCluster:
    def __init__(self):
        self.nodes = [
            {"host": "http://rtx3090-win-1:11434", "model": "deepseek-coder:33b"},
            {"host": "http://rtx3080-win:11434", "model": "deepseek-coder:6.7b"},
            {"host": "http://rtx3070-win:11434", "model": "codellama:7b"},
            {"host": "http://tesla-p40:11434", "model": "codellama:7b"},
            {"host": "http://home-ai-server:11434", "model": "smollm2:1.7b"},
        ]
    
    async def process_distributed(self, chunks):
        # Check available nodes
        available_nodes = await self.discover_available_nodes()
        
        # Distribute processing across nodes
        return await self.parallel_process(chunks, available_nodes)
```

#### Revenue-Aware Scheduling
```python
def calculate_processing_cost(processing_time_minutes):
    # With $160/month total Salad revenue (~$0.22/hour average)
    hourly_rate = 0.22
    cost = (processing_time_minutes / 60) * hourly_rate
    return cost  # Typically $0.05-0.75 per processing run

def should_use_rtx_cards(urgency="normal"):
    # With such low opportunity cost, almost always yes
    return True
```

---

## 📊 Performance Tracking

### Current RAG Metrics (Baseline)
- **Avg Chunks Retrieved**: ~10
- **Avg Similarity Score**: ~0.254
- **Context Utilization**: ~60%
- **RAG Retrieval Speed**: ~0.16s
- **Source Files Accessed**: ~4

### Target Metrics (Post-Implementation)

#### After Context-Aware LLM Summaries (Phase 2)
- **Avg Chunks Retrieved**: 12-18
- **Avg Similarity Score**: 0.5-0.7 (from context-aware summaries)
- **Context Utilization**: 80-90%
- **RAG Retrieval Speed**: <1s (maintain)
- **Source Files Accessed**: 8-12
- **Cross-Reference Quality**: Significantly improved

#### After Embedding Upgrade (Phase 4, if implemented)
- **Avg Similarity Score**: 0.5-0.7 (combined LLM summaries + better embeddings)
- **Query Success Rate**: +25-35% total improvement
- **Cross-Reference Quality**: Significantly improved
- **Semantic Understanding**: Much better "how does X work?" responses

#### Combined Impact Estimation (Context-Aware)
- **Context-Aware LLM Summaries**: +20-30% improvement (primary gain)
- **Embedding Upgrade**: +10-20% additional improvement (secondary gain)
- **Total Expected**: +30-50% overall RAG system improvement
- **Architectural Understanding**: Dramatically improved for module-level queries

---

## 🔄 Implementation Sequence

1. **Phase 1**: Implement distributed RTX processing with existing models
   - Set up Ollama v0.9.5 network exposure on Windows RTX machines
   - Implement distributed coordination and load balancing
   - Keep current `nomic-embed-text` embedding model (stable baseline)

2. **Phase 2**: Add LLM-generated summaries using optimal models
   - Use RTX 3090s with `deepseek-coder:33b` for high-quality summaries
   - Process 2,283 chunks in 10-20 minutes (~$0.07-0.18 cost)
   - Implement dual embedding approach (code + summaries)

3. **Phase 3**: Test and measure impact on existing query suite
   - Measure similarity score improvement from LLM summaries
   - Expected improvement: 0.254 → 0.4-0.6 similarity scores
   - Validate +15-25% query success rate improvement

4. **Phase 4**: Evaluate embedding model upgrade
   - A/B test `mxbai-embed-large` vs current `nomic-embed-text`
   - Test on subset of codebase first
   - Full migration if >10% performance improvement demonstrated

5. **Phase 5**: Optimize and consider advanced features
   - Implement comment extraction if embedding upgrade shows benefits
   - Fine-tune distributed processing performance
   - Consider specialized embedding models for specific use cases

6. **Phase 6**: Production optimization
   - Monitor and optimize performance vs cost trade-offs
   - Implement intelligent caching and query optimization
   - Consider advanced RAG techniques based on performance gains

---

## 📝 Notes

- LLM summaries address semantic understanding (primary weakness)
- Comment extraction addresses architectural documentation (secondary need)
- Distributed processing enables high-performance at low cost
- Implementation should be incremental with measurement at each step
- Current test suite provides excellent baseline for measuring improvements

---

*Last Updated: 2025-07-03*
*Based on analysis of test_openwebui_api.py query patterns and current RAG performance metrics*
