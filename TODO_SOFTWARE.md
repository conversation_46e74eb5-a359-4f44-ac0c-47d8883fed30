# TODO: RAG System Software Enhancements

## 🚀 High Priority Software Improvements

### 1. LLM-Generated Chunk Summaries
**Status**: 📋 Planned  
**Priority**: High  
**Estimated Impact**: +15-25% success rate improvement

#### Description
Process each code chunk with an LLM to generate 1-3 sentence summaries describing the chunk's purpose, functionality, and role in the larger system.

#### Implementation Options
- **Option 1 (Recommended)**: Dual Embedding - Store both original code AND summary
- **Option 2**: Enhanced Content - Prepend summary to original code content
- **Option 3**: Summary-Only - Replace code with summaries (not recommended)

#### Expected Benefits
- **Semantic Matching**: Better understanding of "how does X work?" queries
- **Pattern Recognition**: Improved detection of architectural patterns
- **Cross-Reference**: Better relationships between related code chunks
- **Similarity Scores**: Expected improvement from ~0.254 to 0.4-0.6
- **Context Utilization**: Expected improvement from ~60% to 75-85%

#### Implementation Details
```python
# Example prompt for LLM processing
prompt = """
Analyze this code chunk and provide a 1-3 sentence summary describing:
1. What this code does (primary purpose)
2. Key functionality or algorithms used  
3. How it fits into the larger system

Code chunk:
{chunk_content}

Summary:
"""
```

#### Ollama Model Recommendations
Based on available models, optimal choices for chunk summarization:

| Model | Speed | Quality | VRAM | Best For |
|-------|-------|---------|------|----------|
| **deepseek-coder:6.7b** | ⚡ Fast | 🎯 Excellent | 8-10 GB | **Primary choice** |
| **codellama:7b-instruct** | ⚡ Fast | 🎯 Very Good | 8-10 GB | Alternative |
| **smollm2:1.7b** | 🚀 Ultra Fast | ✅ Good | 2-3 GB | Testing/prototyping |
| **deepseek-coder:33b** | 🐌 Slower | 🎯 Excellent | 20-24 GB | High-quality option |

#### Processing Implementation
```python
async def process_chunks_with_summaries():
    # Use distributed RTX processing for speed
    cluster = DistributedOllamaCluster()
    
    # Process in parallel across available RTX cards
    semaphore = asyncio.Semaphore(4)  # Limit concurrent requests
    
    async def process_chunk(chunk):
        async with semaphore:
            return await cluster.summarize_chunk(chunk)
    
    # Process all chunks in parallel
    tasks = [process_chunk(chunk) for chunk in chunks]
    summaries = await asyncio.gather(*tasks)
    
    # Expected time with RTX 3090s: 10-20 minutes for 2,283 chunks
```

#### Success Metrics
- Measure improvement on existing test query suite
- Target categories: Memory Management, Timer Management, Error Handling
- Expected query success rate improvement: +15-25%

---

### 2. Standalone Comment Block Extraction
**Status**: 📋 Planned  
**Priority**: Medium  
**Estimated Impact**: +5-10% for architectural queries

#### Description
Extract standalone comment blocks, file headers, and API documentation as separate searchable chunks in addition to the current code-embedded approach.

#### Current State
- Comments are **counted and analyzed** but not extracted as separate chunks
- Comments are included within function/class chunks but not independently searchable
- File header comments are filtered out during header processing

#### Proposed Enhancement
Extract as separate chunks:
1. **File Header Comments** - Overall file/module documentation
2. **API Documentation Comments** - Function/class documentation blocks
3. **Large Comment Blocks** - Design explanations, algorithm descriptions
4. **Inline Documentation** - Significant explanatory comments

#### Implementation Approach
```python
def extract_comment_blocks(source_code, filepath, language):
    """Extract standalone comment blocks as separate chunks"""
    comment_chunks = []
    
    # Extract file header comments
    header_comments = extract_file_header_comments(source_code, language)
    
    # Extract function/class documentation
    api_docs = extract_api_documentation(source_code, language)
    
    # Extract large comment blocks
    block_comments = extract_large_comment_blocks(source_code, language)
    
    return comment_chunks
```

#### Target Query Types
- `"What are the main modules in this codebase?"`
- `"What is the overall architecture philosophy?"`
- `"Show me API documentation for memory management"`
- `"What are the design principles behind TMW library?"`

#### Implementation Priority
- **Implement AFTER** LLM summaries to avoid diluting current performance
- **A/B Test** impact on existing query suite before full deployment

---

### 3. RAG Server API Enhancements
**Status**: ✅ Partially Complete  
**Priority**: Medium

#### Completed Fixes
- ✅ Fixed RAG Query HTTP 404 errors
- ✅ Updated endpoint from `/api/query` → `/tools/search_code`
- ✅ Fixed payload format: `codebase` → `codebase_name`, `max_chunks` → `n_results`
- ✅ Added response parsing for RAG server format
- ✅ Added type annotation for `quality_counts: dict[str, int]`

#### Remaining Enhancements
- **Parallel RAG Querying**: Enhance coordination between OpenWebUI and direct RAG server
- **Context Quality Metrics**: Improve analysis of RAG context utilization
- **Chunk Count Reporting**: Better handling of zero-chunk scenarios
- **Cross-Reference Enhancement**: Improve relationships between code chunks

---

### 4. Distributed Processing Coordination
**Status**: 📋 Planned  
**Priority**: High

#### Ollama v0.9.5 Network Exposure
- **Network Access**: Ollama can now be exposed across network devices
- **Cross-Platform**: Windows and Linux machines work seamlessly
- **Simplified Coordination**: Direct HTTP API calls, no SSH/PowerShell remoting needed

#### Implementation Architecture
```python
class DistributedOllamaCluster:
    def __init__(self):
        self.nodes = [
            {"host": "http://rtx3090-win-1:11434", "model": "deepseek-coder:33b"},
            {"host": "http://rtx3080-win:11434", "model": "deepseek-coder:6.7b"},
            {"host": "http://rtx3070-win:11434", "model": "codellama:7b"},
            {"host": "http://tesla-p40:11434", "model": "codellama:7b"},
            {"host": "http://home-ai-server:11434", "model": "smollm2:1.7b"},
        ]
    
    async def process_distributed(self, chunks):
        # Check available nodes
        available_nodes = await self.discover_available_nodes()
        
        # Distribute processing across nodes
        return await self.parallel_process(chunks, available_nodes)
```

#### Revenue-Aware Scheduling
```python
def calculate_processing_cost(processing_time_minutes):
    # With $160/month total Salad revenue (~$0.22/hour average)
    hourly_rate = 0.22
    cost = (processing_time_minutes / 60) * hourly_rate
    return cost  # Typically $0.05-0.75 per processing run

def should_use_rtx_cards(urgency="normal"):
    # With such low opportunity cost, almost always yes
    return True
```

---

## 📊 Performance Tracking

### Current RAG Metrics (Baseline)
- **Avg Chunks Retrieved**: ~10
- **Avg Similarity Score**: ~0.254
- **Context Utilization**: ~60%
- **RAG Retrieval Speed**: ~0.16s
- **Source Files Accessed**: ~4

### Target Metrics (Post-Implementation)
- **Avg Chunks Retrieved**: 12-15
- **Avg Similarity Score**: 0.4-0.6
- **Context Utilization**: 75-85%
- **RAG Retrieval Speed**: <1s (maintain)
- **Source Files Accessed**: 6-8

---

## 🔄 Implementation Sequence

1. **Phase 1**: Implement distributed RTX processing with existing models
2. **Phase 2**: Add LLM-generated summaries using optimal models
3. **Phase 3**: Test and measure impact on existing query suite
4. **Phase 4**: Optimize based on results and consider comment extraction
5. **Phase 5**: Implement advanced features based on performance gains

---

## 📝 Notes

- LLM summaries address semantic understanding (primary weakness)
- Comment extraction addresses architectural documentation (secondary need)
- Distributed processing enables high-performance at low cost
- Implementation should be incremental with measurement at each step
- Current test suite provides excellent baseline for measuring improvements

---

*Last Updated: 2025-07-03*
*Based on analysis of test_openwebui_api.py query patterns and current RAG performance metrics*
