{"data_mtime": 1751504572, "dep_lines": [10, 26, 38, 39, 40, 53, 54, 55, 56, 57, 64, 65, 71, 72, 73, 1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 28, 29, 36, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["http.cookies", "urllib.parse", "aiohttp.hdrs", "aiohttp.abc", "aiohttp.helpers", "aiohttp.http_parser", "aiohttp.http_writer", "aiohttp.multipart", "aiohttp.streams", "aiohttp.typedefs", "aiohttp.web_exceptions", "aiohttp.web_response", "aiohttp.web_app", "aiohttp.web_protocol", "aiohttp.web_urldispatcher", "asyncio", "datetime", "io", "re", "socket", "string", "tempfile", "types", "warnings", "typing", "attr", "multidict", "yarl", "aiohttp", "builtins", "_asyncio", "_frozen_importlib", "_io", "abc", "aiohttp.base_protocol", "asyncio.events", "asyncio.protocols", "asyncio.transports", "attr.setters", "enum", "json", "json.decoder", "os", "propcache", "propcache._helpers", "propcache._helpers_py", "typing_extensions", "yarl._url"], "hash": "b4e538296f32211378c3e81f785fbbe091133ff2", "id": "aiohttp.web_request", "ignore_all": true, "interface_hash": "08f8bd67cf6a28eaad8e46d15ad5e2941dcee647", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_request.py", "plugin_data": null, "size": 30666, "suppressed": [], "version_id": "1.15.0"}