{"data_mtime": 1751504572, "dep_lines": [20, 21, 45, 46, 50, 53, 54, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 22, 23, 24, 25, 48, 49, 51, 53, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["email.parser", "email.utils", "urllib.parse", "urllib.request", "propcache.api", "aiohttp.hdrs", "aiohttp.log", "asyncio", "base64", "<PERSON><PERSON><PERSON><PERSON>", "contextlib", "datetime", "enum", "functools", "inspect", "netrc", "os", "platform", "re", "sys", "time", "weakref", "collections", "math", "pathlib", "types", "typing", "attr", "multidict", "yarl", "aiohttp", "builtins", "_asyncio", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "asyncio.timeouts", "attr.setters", "email", "email._policybase", "email.message", "yarl._url"], "hash": "ed91920703121ad4c36185a70888481831973135", "id": "aiohttp.helpers", "ignore_all": true, "interface_hash": "19dd6862761616d77b468936f2dcd0588c4747ef", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\helpers.py", "plugin_data": null, "size": 30035, "suppressed": [], "version_id": "1.15.0"}