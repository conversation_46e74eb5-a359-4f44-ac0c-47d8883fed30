{"data_mtime": 1751504572, "dep_lines": [30, 31, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 1, 2, 3, 4, 5, 27, 28, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aiohttp.hdrs", "aiohttp.abc", "aiohttp.helpers", "aiohttp.http_parser", "aiohttp.log", "aiohttp.streams", "aiohttp.typedefs", "aiohttp.web_exceptions", "aiohttp.web_log", "aiohttp.web_middlewares", "aiohttp.web_protocol", "aiohttp.web_request", "aiohttp.web_response", "aiohttp.web_routedef", "aiohttp.web_server", "aiohttp.web_urldispatcher", "asyncio", "logging", "warnings", "functools", "typing", "aiosignal", "frozenlist", "aiohttp", "builtins", "_asyncio", "_frozen_importlib", "abc", "aiohttp.base_protocol", "aiohttp.http_writer", "asyncio.events", "asyncio.protocols", "multidict", "types", "yarl", "yarl._url"], "hash": "921ec3cfb0bdfdc21466fecdd6c367d3188e055c", "id": "aiohttp.web_app", "ignore_all": true, "interface_hash": "c2098ac71128555af84da097fb6aaa445972d626", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_app.py", "plugin_data": null, "size": 20174, "suppressed": [], "version_id": "1.15.0"}