{"data_mtime": 1751504569, "dep_lines": [4, 9, 3, 5, 7, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "yarl._quoters", "math", "typing", "multidict", "builtins", "_frozen_importlib", "abc"], "hash": "9a3fca1c4982f09b67f3a7551085dd1c8e9359e9", "id": "yarl._query", "ignore_all": true, "interface_hash": "9f3524fd2bf3b957f8804004d31bb1f65dcea3c5", "mtime": 1750470764, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\yarl\\_query.py", "plugin_data": null, "size": 3883, "suppressed": [], "version_id": "1.15.0"}